-- =====================================================
-- Supabase Database Setup for Intakhabat App
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. User Profiles Table
-- =====================================================

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    phone TEXT,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON public.user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);

-- =====================================================
-- 2. Voters Table
-- =====================================================

-- Create voters table
CREATE TABLE IF NOT EXISTS public.voters (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    national_id TEXT NOT NULL UNIQUE,
    committee_location TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_voters_national_id ON public.voters(national_id);
CREATE INDEX IF NOT EXISTS idx_voters_name ON public.voters(name);
CREATE INDEX IF NOT EXISTS idx_voters_committee_location ON public.voters(committee_location);
CREATE INDEX IF NOT EXISTS idx_voters_created_at ON public.voters(created_at);

-- =====================================================
-- 3. Committee Locations Table (for suggestions)
-- =====================================================

-- Create committee_locations table for dropdown suggestions
CREATE TABLE IF NOT EXISTS public.committee_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    governorate TEXT,
    district TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_committee_locations_name ON public.committee_locations(name);
CREATE INDEX IF NOT EXISTS idx_committee_locations_governorate ON public.committee_locations(governorate);

-- Insert some sample committee locations
INSERT INTO public.committee_locations (name, governorate, district) VALUES
('لجنة مدرسة الشهيد أحمد زويل', 'القاهرة', 'مصر الجديدة'),
('لجنة مدرسة النصر الابتدائية', 'القاهرة', 'شبرا'),
('لجنة مدرسة الحرية الثانوية', 'الجيزة', 'الدقي'),
('لجنة مدرسة المستقبل', 'الجيزة', 'المهندسين'),
('لجنة مدرسة الأمل الإعدادية', 'الإسكندرية', 'المنتزه'),
('لجنة مدرسة النيل الثانوية', 'الإسكندرية', 'سيدي جابر')
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- 4. Row Level Security (RLS) Policies
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.voters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.committee_locations ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- User Profiles Policies
-- =====================================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Admins can view all profiles
CREATE POLICY "Admins can view all profiles" ON public.user_profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =====================================================
-- Voters Policies
-- =====================================================

-- All authenticated users can view voters
CREATE POLICY "Authenticated users can view voters" ON public.voters
    FOR SELECT USING (auth.role() = 'authenticated');

-- Only admins can insert voters
CREATE POLICY "Admins can insert voters" ON public.voters
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Only admins can update voters
CREATE POLICY "Admins can update voters" ON public.voters
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Only admins can delete voters
CREATE POLICY "Admins can delete voters" ON public.voters
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =====================================================
-- Committee Locations Policies
-- =====================================================

-- All authenticated users can view committee locations
CREATE POLICY "Authenticated users can view committee locations" ON public.committee_locations
    FOR SELECT USING (auth.role() = 'authenticated');

-- Only admins can modify committee locations
CREATE POLICY "Admins can modify committee locations" ON public.committee_locations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =====================================================
-- 5. Functions and Triggers
-- =====================================================

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for user_profiles
CREATE TRIGGER handle_updated_at_user_profiles
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Trigger for voters
CREATE TRIGGER handle_updated_at_voters
    BEFORE UPDATE ON public.voters
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, role)
    VALUES (NEW.id, NEW.email, 'user');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on auth.users insert
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 6. Views for easier data access
-- =====================================================

-- View for voters with creator information
CREATE OR REPLACE VIEW public.voters_with_creator AS
SELECT 
    v.*,
    up.email as created_by_email,
    up.role as created_by_role
FROM public.voters v
LEFT JOIN public.user_profiles up ON v.created_by = up.id;

-- =====================================================
-- 7. Sample Data (Optional - for testing)
-- =====================================================

-- Create a sample admin user (you'll need to replace with actual user ID after signup)
-- INSERT INTO public.user_profiles (id, email, role) VALUES
-- ('your-admin-user-id-here', '<EMAIL>', 'admin')
-- ON CONFLICT (id) DO UPDATE SET role = 'admin';

-- =====================================================
-- 8. Grant necessary permissions
-- =====================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO anon, authenticated;

-- Grant permissions on tables
GRANT ALL ON public.user_profiles TO authenticated;
GRANT ALL ON public.voters TO authenticated;
GRANT ALL ON public.committee_locations TO authenticated;

-- Grant permissions on sequences
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
