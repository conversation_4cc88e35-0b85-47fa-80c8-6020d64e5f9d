# صفحة إدارة المستخدمين والصلاحيات

## نظرة عامة
تم إنشاء نظام شامل لإدارة المستخدمين والصلاحيات يسمح للمسؤولين بإنشاء وإدارة حسابات المستخدمين وتعديل صلاحياتهم.

## الميزات الجديدة

### 🔐 إدارة المستخدمين
- **عرض قائمة المستخدمين:** قائمة شاملة بجميع المستخدمين مع معلوماتهم
- **إنشاء مستخدمين جدد:** إضافة مستخدمين جدد مع تحديد الصلاحيات
- **تعديل بيانات المستخدمين:** تحديث البريد الإلكتروني ورقم الهاتف
- **حذف المستخدمين:** إزالة المستخدمين (ما عدا الحساب الحالي)
- **تغيير الصلاحيات:** ترقية/تخفيض صلاحيات المستخدمين

### 📊 الإحصائيات والمراقبة
- **إجمالي المستخدمين:** عدد جميع المستخدمين في النظام
- **عدد المسؤولين:** إحصائية المستخدمين بصلاحية مسؤول
- **عدد المستخدمين العاديين:** إحصائية المستخدمين العاديين
- **البحث والتصفية:** بحث بالبريد الإلكتروني أو رقم الهاتف

### 🛡️ الأمان والصلاحيات
- **وصول محدود:** فقط المسؤولين يمكنهم الوصول لصفحة إدارة المستخدمين
- **حماية الحساب الشخصي:** لا يمكن حذف أو تعديل صلاحية الحساب الحالي
- **تأكيد العمليات:** تأكيد قبل الحذف أو تغيير الصلاحيات

## الملفات الجديدة

### `lib/providers/users_provider.dart`
Provider شامل لإدارة حالة المستخدمين:
- **UsersState:** حالة قائمة المستخدمين والبحث والتحميل
- **UsersNotifier:** منطق إدارة المستخدمين (CRUD operations)
- **Helper Providers:** providers مساعدة للإحصائيات والحالات

### `lib/widgets/user_card.dart`
Widget لعرض بيانات المستخدم:
- **عرض معلومات المستخدم:** البريد الإلكتروني والصلاحية والهاتف
- **تمييز بصري للصلاحيات:** ألوان وأيقونات مختلفة للمسؤولين والمستخدمين
- **قائمة إجراءات:** تعديل، حذف، تغيير صلاحية
- **حماية الحساب الحالي:** إخفاء خيارات الحذف/التعديل للحساب الحالي

### `lib/widgets/user_form_dialog.dart`
نموذج إضافة/تعديل المستخدمين:
- **إنشاء مستخدم جديد:** بريد إلكتروني، كلمة مرور، هاتف، صلاحية
- **تعديل مستخدم موجود:** تحديث البيانات (بدون كلمة المرور)
- **اختيار الصلاحية:** مستخدم عادي أو مسؤول
- **التحقق من البيانات:** validation شامل للحقول

### `lib/screens/users_management_screen.dart`
الشاشة الرئيسية لإدارة المستخدمين:
- **واجهة شاملة:** قائمة المستخدمين مع البحث والإحصائيات
- **إجراءات سريعة:** إضافة، تعديل، حذف، تغيير صلاحية
- **تحميل تدريجي:** Pagination للأداء الأمثل
- **حماية الوصول:** فقط للمسؤولين

## التحديثات على الملفات الموجودة

### `lib/services/supabase_service.dart`
إضافة دوال إدارة المستخدمين:
- **getUsers():** جلب قائمة المستخدمين مع البحث والتصفية
- **getUsersCount():** عدد المستخدمين للإحصائيات
- **createUserByAdmin():** إنشاء مستخدم جديد بواسطة المسؤول
- **deleteUser():** حذف مستخدم (مع حماية الحساب الحالي)
- **updateUserProfile():** تحديث بيانات المستخدم

### `lib/config/app_router.dart`
إضافة route جديد:
- **Route:** `/users-management`
- **Navigation helpers:** `goToUsersManagement()` و `pushUsersManagement()`

### `lib/widgets/custom_app_bar.dart`
إضافة زر إدارة المستخدمين:
- **زر جديد:** أيقونة المستخدمين في شريط التطبيق
- **ظهور شرطي:** فقط للمسؤولين
- **تصميم متسق:** نفس تصميم زر الإحصائيات

### `lib/screens/home_screen.dart`
ربط زر إدارة المستخدمين:
- **تمرير callback:** `onUsersManagement` للـ CustomAppBar
- **تحقق من الصلاحية:** فقط للمسؤولين

## كيفية الاستخدام

### للمسؤولين:
1. **الوصول للصفحة:** اضغط على أيقونة المستخدمين في شريط التطبيق
2. **إضافة مستخدم جديد:** اضغط زر "+" واملأ البيانات المطلوبة
3. **تعديل مستخدم:** اضغط على النقاط الثلاث → تعديل
4. **تغيير صلاحية:** اضغط على النقاط الثلاث → تغيير الصلاحية
5. **حذف مستخدم:** اضغط على النقاط الثلاث → حذف (مع تأكيد)
6. **البحث:** استخدم شريط البحث للعثور على مستخدم معين

### للمستخدمين العاديين:
- **لا يمكن الوصول:** ستظهر رسالة "ليس لديك صلاحية للوصول إلى هذه الصفحة"

## الأمان والحماية

### 🔒 حماية على مستوى الواجهة:
- إخفاء زر إدارة المستخدمين للمستخدمين العاديين
- رسالة منع الوصول للمستخدمين غير المصرحين
- إخفاء خيارات الحذف/التعديل للحساب الحالي

### 🛡️ حماية على مستوى الخادم:
- التحقق من صلاحية المسؤول في جميع العمليات
- منع حذف الحساب الحالي
- Row Level Security (RLS) في قاعدة البيانات

### 🔐 حماية البيانات:
- تشفير كلمات المرور
- التحقق من صحة البريد الإلكتروني
- منع تكرار البريد الإلكتروني

## الإحصائيات المتاحة

### 📊 في الشاشة الرئيسية:
- **إجمالي المستخدمين:** العدد الكلي
- **المسؤولين:** عدد المستخدمين بصلاحية مسؤول
- **المستخدمين العاديين:** عدد المستخدمين العاديين

### 🔍 إمكانيات البحث:
- البحث بالبريد الإلكتروني
- البحث برقم الهاتف
- البحث الفوري أثناء الكتابة

## التصميم والواجهة

### 🎨 تصميم متسق:
- نفس نمط التصميم المستخدم في باقي التطبيق
- ألوان مميزة للصلاحيات (بنفسجي للمسؤولين، أزرق للمستخدمين)
- أيقونات واضحة ومعبرة

### 📱 تجاوب مع الشاشات:
- يعمل على جميع أحجام الشاشات
- تخطيط مرن للإحصائيات
- قوائم قابلة للتمرير

## المتطلبات

### ✅ للمسؤولين:
- حساب بصلاحية مسؤول
- اتصال بالإنترنت
- صلاحيات قاعدة البيانات

### ⚠️ ملاحظات مهمة:
- يجب وجود مسؤول واحد على الأقل في النظام
- لا يمكن تخفيض صلاحية آخر مسؤول
- حذف المستخدم يحذف جميع بياناته المرتبطة

---

**تاريخ الإنشاء:** 3 أغسطس 2025  
**الحالة:** ✅ جاهز للاستخدام  
**المطور:** Augment Agent
