class CommitteeLocationModel {
  final String? id;
  final String name;
  final String? governorate;
  final String? district;
  final bool isActive;
  final DateTime? createdAt;

  CommitteeLocationModel({
    this.id,
    required this.name,
    this.governorate,
    this.district,
    this.isActive = true,
    this.createdAt,
  });

  factory CommitteeLocationModel.fromJson(Map<String, dynamic> json) {
    return CommitteeLocationModel(
      id: json['id']?.toString(),
      name: json['name'] ?? '',
      governorate: json['governorate'],
      district: json['district'],
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'name': name,
      if (governorate != null) 'governorate': governorate,
      if (district != null) 'district': district,
      'is_active': isActive,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
    };
  }

  CommitteeLocationModel copyWith({
    String? id,
    String? name,
    String? governorate,
    String? district,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return CommitteeLocationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      governorate: governorate ?? this.governorate,
      district: district ?? this.district,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommitteeLocationModel &&
        other.id == id &&
        other.name == name &&
        other.governorate == governorate &&
        other.district == district;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        governorate.hashCode ^
        district.hashCode;
  }

  @override
  String toString() {
    return 'CommitteeLocationModel(id: $id, name: $name, governorate: $governorate, district: $district)';
  }
}
