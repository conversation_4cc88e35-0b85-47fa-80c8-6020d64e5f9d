# إعداد البيانات التجريبية

## ⚠️ تنبيه مهم

**يجب تشغيل `database_setup.sql` أولاً قبل إضافة المستخدمين التجريبيين!**

## خطوات إضافة المستخدمين التجريبيين

### الخطوة 1: تشغيل إعداد قاعدة البيانات

1. في Supabase Dashboard، اذهب إلى **SQL Editor**
2. انسخ والصق محتوى ملف `database_setup.sql`
3. اضغط **Run** - سيتم إنشاء الجداول والفهارس والسياسات
4. تأكد من ظهور رسالة "تم إعداد قاعدة البيانات بنجاح!"

### الخطوة 2: إنشاء المستخدمين في Supabase Auth

1. اذهب إلى **Authentication** > **Users**
2. اض<PERSON><PERSON> على **"Add user"**

#### المستخدم المسؤول:
- **Email**: <EMAIL>
- **Password**: 123456789
- **Auto Confirm User**: ✅ مفعل

#### المستخدم العادي:
- **Email**: <EMAIL>
- **Password**: 123456789
- **Auto Confirm User**: ✅ مفعل

### الخطوة 3: الحصول على User IDs

بعد إنشاء المستخدمين:
1. في قائمة **Users**، انسخ **User UID** لكل مستخدم
2. احفظ هذه المعرفات للخطوة التالية

### الخطوة 4: تحديث ملف demo_users_setup.sql

1. افتح ملف `demo_users_setup.sql`
2. استبدل `ADMIN_USER_ID_FROM_SUPABASE` بـ User UID للمسؤول
3. استبدل `REGULAR_USER_ID_FROM_SUPABASE` بـ User UID للمستخدم العادي

مثال:
```sql
INSERT INTO user_profiles (id, username, email, role) VALUES
  ('a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'admin', '<EMAIL>', 'admin'),
  ('b2c3d4e5-f6g7-8901-bcde-f23456789012', 'user', '<EMAIL>', 'user')
ON CONFLICT (id) DO NOTHING;
```

### الخطوة 5: تشغيل ملف المستخدمين التجريبيين

1. في **SQL Editor**، انسخ والصق محتوى `demo_users_setup.sql` المحدث
2. اضغط **Run**
3. تأكد من ظهور رسائل النجاح

### 4. اختبار تسجيل الدخول

الآن يمكنك تسجيل الدخول باستخدام:

#### المسؤول:
- **اسم المستخدم**: admin
- **كلمة المرور**: 123456789

#### المستخدم العادي:
- **اسم المستخدم**: user
- **كلمة المرور**: 123456789

---

## إضافة مستخدمين إضافيين

### طريقة SQL (أسرع):

```sql
-- استبدل UUIDs بالقيم الفعلية من Supabase Auth
INSERT INTO user_profiles (id, username, email, role) VALUES
  ('UUID_FROM_SUPABASE_AUTH_1', 'admin', '<EMAIL>', 'admin'),
  ('UUID_FROM_SUPABASE_AUTH_2', 'user', '<EMAIL>', 'user'),
  ('UUID_FROM_SUPABASE_AUTH_3', 'manager', '<EMAIL>', 'admin'),
  ('UUID_FROM_SUPABASE_AUTH_4', 'viewer', '<EMAIL>', 'user')
ON CONFLICT (id) DO NOTHING;
```

### طريقة واجهة المستخدم:

1. أنشئ المستخدم في **Authentication** > **Users**
2. أضف بياناته في **Table Editor** > **user_profiles**

---

## البيانات التجريبية للناخبين

البيانات التالية ستُضاف تلقائياً عند تشغيل `database_setup.sql`:

| الاسم | الرقم القومي | مكان اللجنة |
|-------|-------------|-------------|
| أحمد محمد علي | 29012011234567 | مدرسة الشهيد أحمد زويل الابتدائية |
| فاطمة أحمد حسن | 29505021234568 | مدرسة النصر الإعدادية |
| محمد عبد الله محمود | 28803151234569 | مدرسة الحرية الثانوية |
| عائشة محمد إبراهيم | 29201101234570 | مدرسة الأمل الابتدائية |
| يوسف أحمد عبد الرحمن | 28706201234571 | مدرسة المستقبل الإعدادية |

---

## اختبار الصلاحيات

### المسؤول (admin):
- ✅ عرض جميع الناخبين
- ✅ إضافة ناخبين جدد
- ✅ تعديل بيانات الناخبين
- ✅ حذف الناخبين
- ✅ البحث والتصفية

### المستخدم العادي (user):
- ✅ عرض جميع الناخبين
- ❌ إضافة ناخبين جدد
- ❌ تعديل بيانات الناخبين
- ❌ حذف الناخبين
- ✅ البحث والتصفية

---

## استكشاف الأخطاء

### خطأ "اسم المستخدم غير موجود":
- تأكد من إضافة المستخدم في جدول `user_profiles`
- تأكد من صحة `username` في الجدول

### خطأ "كلمة المرور خاطئة":
- تأكد من إنشاء المستخدم في Supabase Auth
- تأكد من صحة كلمة المرور

### خطأ "صلاحيات غير كافية":
- تأكد من صحة `role` في جدول `user_profiles`
- تأكد من أن القيمة إما `admin` أو `user`

---

## نصائح مهمة

1. **احفظ User IDs** في مكان آمن
2. **استخدم كلمات مرور قوية** في الإنتاج
3. **لا تشارك بيانات الاختبار** مع أحد
4. **احذف البيانات التجريبية** قبل النشر

---

## إضافة مستخدمين جدد من التطبيق

حالياً، التطبيق يدعم تسجيل الدخول فقط. لإضافة مستخدمين جدد:

1. أنشئ المستخدم في Supabase Auth Dashboard
2. أضف بياناته في جدول `user_profiles`
3. أو طور ميزة التسجيل في التطبيق (اختياري)

**البيانات التجريبية جاهزة للاستخدام! 🎉**
