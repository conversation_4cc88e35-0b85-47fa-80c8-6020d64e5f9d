import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../models/user_model.dart';
import '../providers/users_provider.dart';
import '../utils/validators.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';

class UserFormDialog extends ConsumerStatefulWidget {
  final UserModel? user; // null for create, UserModel for edit

  const UserFormDialog({super.key, this.user});

  @override
  ConsumerState<UserFormDialog> createState() => _UserFormDialogState();
}

class _UserFormDialogState extends ConsumerState<UserFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();

  UserRole _selectedRole = UserRole.user;
  bool _isLoading = false;

  bool get isEditing => widget.user != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _emailController.text = widget.user!.email;
      _phoneController.text = widget.user!.phone ?? '';
      _selectedRole = widget.user!.role;
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    bool success;
    if (isEditing) {
      // Update existing user
      final updatedUser = widget.user!.copyWith(
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty 
            ? null 
            : _phoneController.text.trim(),
        role: _selectedRole,
      );
      success = await ref.read(usersProvider.notifier).updateUser(updatedUser);
    } else {
      // Create new user
      success = await ref.read(usersProvider.notifier).createUser(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        phone: _phoneController.text.trim().isEmpty 
            ? null 
            : _phoneController.text.trim(),
        role: _selectedRole,
      );
    }

    setState(() {
      _isLoading = false;
    });

    if (success && mounted) {
      Navigator.of(context).pop(true);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEditing ? 'تم تحديث المستخدم بنجاح' : 'تم إنشاء المستخدم بنجاح',
          ),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      final error = ref.read(usersErrorProvider);
      if (error != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(error),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(isEditing ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Email Field
                CustomTextField(
                  controller: _emailController,
                  label: 'البريد الإلكتروني *',
                  hint: 'أدخل البريد الإلكتروني',
                  prefixIcon: Icons.email,
                  keyboardType: TextInputType.emailAddress,
                  validator: Validators.combine([
                    Validators.required,
                    Validators.email,
                  ]),
                  enabled: !_isLoading,
                ),

                const SizedBox(height: 16),

                // Password Field (only for new users)
                if (!isEditing) ...[
                  CustomTextField(
                    controller: _passwordController,
                    label: 'كلمة المرور *',
                    hint: 'أدخل كلمة المرور',
                    prefixIcon: Icons.lock,
                    obscureText: true,
                    validator: Validators.combine([
                      Validators.required,
                      (value) {
                        if (value != null && value.length < 6) {
                          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                        }
                        return null;
                      },
                    ]),
                    enabled: !_isLoading,
                  ),
                  const SizedBox(height: 16),
                ],

                // Phone Field
                CustomTextField(
                  controller: _phoneController,
                  label: 'رقم الهاتف',
                  hint: 'أدخل رقم الهاتف (اختياري)',
                  prefixIcon: Icons.phone,
                  keyboardType: TextInputType.phone,
                  enabled: !_isLoading,
                ),

                const SizedBox(height: 20),

                // Role Selection
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الصلاحية *',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12),
                      RadioListTile<UserRole>(
                        title: const Text('مستخدم عادي'),
                        subtitle: const Text('يمكنه البحث وعرض البيانات فقط'),
                        value: UserRole.user,
                        groupValue: _selectedRole,
                        onChanged: _isLoading
                            ? null
                            : (value) {
                                setState(() {
                                  _selectedRole = value!;
                                });
                              },
                        contentPadding: EdgeInsets.zero,
                      ),
                      RadioListTile<UserRole>(
                        title: const Text('مسؤول'),
                        subtitle: const Text('يمكنه إدارة جميع البيانات والمستخدمين'),
                        value: UserRole.admin,
                        groupValue: _selectedRole,
                        onChanged: _isLoading
                            ? null
                            : (value) {
                                setState(() {
                                  _selectedRole = value!;
                                });
                              },
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        CustomButton(
          onPressed: _isLoading ? null : _handleSave,
          child: _isLoading
              ? const SpinKitThreeBounce(
                  color: Colors.white,
                  size: 20,
                )
              : Text(isEditing ? 'حفظ التعديلات' : 'إنشاء المستخدم'),
        ),
      ],
    );
  }
}
