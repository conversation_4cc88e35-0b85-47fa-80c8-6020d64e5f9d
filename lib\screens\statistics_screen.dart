import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../providers/auth_provider.dart';
import '../providers/voters_provider.dart';
import '../services/supabase_service.dart';
import '../services/export_service.dart';
import '../utils/responsive_helper.dart';
import '../models/user_model.dart';

class StatisticsScreen extends ConsumerStatefulWidget {
  const StatisticsScreen({super.key});

  @override
  ConsumerState<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends ConsumerState<StatisticsScreen> {
  Map<String, int> _statistics = {};
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final stats = await supabaseService.getStatistics();
      setState(() {
        _statistics = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'فشل في تحميل الإحصائيات: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإحصائيات'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStatistics,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitThreeBounce(color: Colors.blue, size: 30),
                  SizedBox(height: 16),
                  Text('جاري تحميل الإحصائيات...'),
                ],
              ),
            )
          : _error != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    _error!,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.red[600]),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadStatistics,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: _loadStatistics,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: ResponsiveHelper.buildResponsiveContainer(
                  context: context,
                  child: Column(
                    children: [
                      SizedBox(
                        height: ResponsiveHelper.getSpacing(context, 20),
                      ),

                      // Main Statistics Cards
                      _buildMainStatsSection(),

                      SizedBox(
                        height: ResponsiveHelper.getSpacing(context, 24),
                      ),

                      // User Information
                      _buildUserInfoSection(authState),

                      SizedBox(
                        height: ResponsiveHelper.getSpacing(context, 24),
                      ),

                      // System Information
                      _buildSystemInfoSection(),

                      SizedBox(
                        height: ResponsiveHelper.getSpacing(context, 24),
                      ),

                      // Quick Actions (Admin only)
                      if (authState.user?.role == UserRole.admin)
                        _buildQuickActionsSection(),

                      SizedBox(
                        height: ResponsiveHelper.getSpacing(context, 40),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildMainStatsSection() {
    return Padding(
      padding: ResponsiveHelper.getScreenPadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإحصائيات الرئيسية',
            style: ResponsiveHelper.getHeadlineStyle(
              context,
            ).copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: ResponsiveHelper.getSpacing(context, 16)),
          ResponsiveHelper.buildResponsiveGrid(
            context: context,
            spacing: ResponsiveHelper.getSpacing(context, 16),
            children: [
              _buildStatCard(
                title: 'إجمالي المسجلين',
                value: '${_statistics['total_voters'] ?? 0}',
                icon: FontAwesomeIcons.users,
                color: Colors.blue,
                subtitle: 'عدد الناخبين المسجلين',
              ),
              _buildStatCard(
                title: 'إجمالي المستخدمين',
                value: '${_statistics['total_users'] ?? 0}',
                icon: FontAwesomeIcons.userGroup,
                color: Colors.green,
                subtitle: 'مستخدمي النظام',
              ),
              _buildStatCard(
                title: 'المسؤولين',
                value: '${_statistics['total_admins'] ?? 0}',
                icon: FontAwesomeIcons.userShield,
                color: Colors.orange,
                subtitle: 'المستخدمين المسؤولين',
              ),
              _buildStatCard(
                title: 'أماكن اللجان',
                value: '${_statistics['total_locations'] ?? 0}',
                icon: FontAwesomeIcons.locationDot,
                color: Colors.purple,
                subtitle: 'اللجان المتاحة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoSection(AuthState authState) {
    return Padding(
      padding: ResponsiveHelper.getScreenPadding(context),
      child: Container(
        padding: EdgeInsets.all(ResponsiveHelper.getSpacing(context, 20)),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: ResponsiveHelper.getCardBorderRadius(context),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.user,
                  color: Theme.of(context).primaryColor,
                  size: ResponsiveHelper.getIconSize(context, 20),
                ),
                SizedBox(width: ResponsiveHelper.getSpacing(context, 12)),
                Text(
                  'معلومات المستخدم',
                  style: ResponsiveHelper.getTitleStyle(
                    context,
                  ).copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            SizedBox(height: ResponsiveHelper.getSpacing(context, 16)),
            _buildInfoRow(
              'البريد الإلكتروني',
              authState.user?.email ?? 'غير محدد',
            ),
            _buildInfoRow('رقم الهاتف', authState.user?.phone ?? 'غير محدد'),
            _buildInfoRow(
              'نوع المستخدم',
              authState.user?.role.displayName ?? 'غير محدد',
            ),
            _buildInfoRow(
              'تاريخ الإنشاء',
              authState.user?.createdAt?.toString().split(' ')[0] ?? 'غير محدد',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemInfoSection() {
    return Padding(
      padding: ResponsiveHelper.getScreenPadding(context),
      child: Container(
        padding: EdgeInsets.all(ResponsiveHelper.getSpacing(context, 20)),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: ResponsiveHelper.getCardBorderRadius(context),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.gear,
                  color: Colors.blue[700],
                  size: ResponsiveHelper.getIconSize(context, 20),
                ),
                SizedBox(width: ResponsiveHelper.getSpacing(context, 12)),
                Text(
                  'معلومات النظام',
                  style: ResponsiveHelper.getTitleStyle(context).copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
            SizedBox(height: ResponsiveHelper.getSpacing(context, 16)),
            _buildInfoRow('إصدار التطبيق', '1.0.0'),
            _buildInfoRow('قاعدة البيانات', 'Supabase'),
            _buildInfoRow('إطار العمل', 'Flutter'),
            _buildInfoRow('آخر تحديث', DateTime.now().toString().split(' ')[0]),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Padding(
      padding: ResponsiveHelper.getScreenPadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات سريعة',
            style: ResponsiveHelper.getHeadlineStyle(
              context,
            ).copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: ResponsiveHelper.getSpacing(context, 16)),
          ResponsiveHelper.buildResponsiveGrid(
            context: context,
            spacing: ResponsiveHelper.getSpacing(context, 12),
            children: [
              _buildActionCard(
                title: 'تصدير البيانات',
                subtitle: 'تصدير قائمة المسجلين',
                icon: FontAwesomeIcons.download,
                color: Colors.green,
                onTap: () => _showExportDialog(),
              ),
              _buildActionCard(
                title: 'نسخ احتياطي',
                subtitle: 'إنشاء نسخة احتياطية',
                icon: FontAwesomeIcons.cloudArrowUp,
                color: Colors.blue,
                onTap: () {
                  // TODO: Implement backup functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('ميزة النسخ الاحتياطي قيد التطوير'),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
  }) {
    return Container(
      padding: EdgeInsets.all(ResponsiveHelper.getSpacing(context, 20)),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: ResponsiveHelper.getIconSize(context, 24),
              ),
              const Spacer(),
              Text(
                value,
                style: ResponsiveHelper.getHeadlineStyle(
                  context,
                ).copyWith(fontWeight: FontWeight.bold, color: color),
              ),
            ],
          ),
          SizedBox(height: ResponsiveHelper.getSpacing(context, 12)),
          Text(
            title,
            style: ResponsiveHelper.getTitleStyle(
              context,
            ).copyWith(fontWeight: FontWeight.w600),
          ),
          if (subtitle != null) ...[
            SizedBox(height: ResponsiveHelper.getSpacing(context, 4)),
            Text(
              subtitle,
              style: ResponsiveHelper.getBodyStyle(context).copyWith(
                color: Colors.grey[600],
                fontSize: ResponsiveHelper.getFontSize(context, 12),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: ResponsiveHelper.getCardBorderRadius(context),
      child: Container(
        padding: EdgeInsets.all(ResponsiveHelper.getSpacing(context, 16)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: ResponsiveHelper.getCardBorderRadius(context),
          border: Border.all(color: Colors.grey[200]!),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: ResponsiveHelper.getIconSize(context, 32),
            ),
            SizedBox(height: ResponsiveHelper.getSpacing(context, 12)),
            Text(
              title,
              style: ResponsiveHelper.getTitleStyle(
                context,
              ).copyWith(fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: ResponsiveHelper.getSpacing(context, 4)),
            Text(
              subtitle,
              style: ResponsiveHelper.getBodyStyle(context).copyWith(
                color: Colors.grey[600],
                fontSize: ResponsiveHelper.getFontSize(context, 12),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: ResponsiveHelper.getSpacing(context, 8)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: ResponsiveHelper.isMobile(context) ? 100 : 120,
            child: Text(
              '$label:',
              style: ResponsiveHelper.getBodyStyle(
                context,
              ).copyWith(fontWeight: FontWeight.w500, color: Colors.grey[700]),
            ),
          ),
          Expanded(
            child: Text(value, style: ResponsiveHelper.getBodyStyle(context)),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر تنسيق التصدير:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('CSV (Excel)'),
              subtitle: const Text('ملف جدول بيانات'),
              onTap: () => _exportData(ExportFormat.csv),
            ),
            ListTile(
              leading: const Icon(Icons.code),
              title: const Text('JSON'),
              subtitle: const Text('ملف بيانات منظم'),
              onTap: () => _exportData(ExportFormat.json),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportData(ExportFormat format) async {
    Navigator.of(context).pop(); // Close dialog

    try {
      // Get voters data
      final votersState = ref.read(votersProvider);
      final voters = votersState.voters;

      if (voters.isEmpty) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('لا توجد بيانات للتصدير')));
        return;
      }

      // Export to clipboard
      await ExportService.copyToClipboard(voters: voters, format: format);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم نسخ البيانات (${format.displayName}) إلى الحافظة',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في التصدير: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
