import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/supabase_service.dart';

// Auth State
class AuthState {
  final bool isLoading;
  final UserModel? user;
  final String? error;
  final bool isAuthenticated;

  AuthState({
    this.isLoading = false,
    this.user,
    this.error,
    this.isAuthenticated = false,
  });

  AuthState copyWith({
    bool? isLoading,
    UserModel? user,
    String? error,
    bool? isAuthenticated,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

// Auth Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final SupabaseService _supabaseService;

  AuthNotifier(this._supabaseService) : super(AuthState()) {
    _init();
  }

  void _init() {
    // Listen to auth state changes
    _supabaseService.authStateChanges.listen((AuthState authState) {
      _handleAuthStateChange(authState);
    });

    // Check current user
    _checkCurrentUser();
  }

  Future<void> _checkCurrentUser() async {
    final currentUser = _supabaseService.currentUser;
    if (currentUser != null) {
      await _loadUserProfile(currentUser.id);
    }
  }

  void _handleAuthStateChange(AuthState authState) {
    if (authState.event == AuthChangeEvent.signedIn) {
      final user = authState.session?.user;
      if (user != null) {
        _loadUserProfile(user.id);
      }
    } else if (authState.event == AuthChangeEvent.signedOut) {
      state = AuthState();
    }
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final userProfile = await _supabaseService.getUserProfile(userId);

      if (userProfile != null) {
        state = state.copyWith(
          isLoading: false,
          user: userProfile,
          isAuthenticated: true,
        );
      } else {
        // Create default user profile if not exists
        final currentUser = _supabaseService.currentUser;
        if (currentUser != null) {
          final newProfile = await _supabaseService.createUserProfile(
            userId: currentUser.id,
            email: currentUser.email ?? '',
            role: UserRole.user, // Default role
          );

          state = state.copyWith(
            isLoading: false,
            user: newProfile,
            isAuthenticated: true,
          );
        }
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<bool> signIn({
    required String emailOrPhone,
    required String password,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Determine if input is email or phone
      final isEmail = emailOrPhone.contains('@');

      AuthResponse response;
      if (isEmail) {
        response = await _supabaseService.signInWithEmail(
          email: emailOrPhone,
          password: password,
        );
      } else {
        response = await _supabaseService.signInWithPhone(
          phone: emailOrPhone,
          password: password,
        );
      }

      if (response.user != null) {
        await _loadUserProfile(response.user!.id);
        return true;
      } else {
        state = state.copyWith(isLoading: false, error: 'فشل في تسجيل الدخول');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<bool> signUp({
    required String email,
    required String password,
    String? phone,
    UserRole role = UserRole.user,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _supabaseService.signUpWithEmail(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Create user profile with additional info
        await _supabaseService.createUserProfile(
          userId: response.user!.id,
          email: email,
          phone: phone,
          role: role,
        );

        await _loadUserProfile(response.user!.id);
        return true;
      } else {
        state = state.copyWith(isLoading: false, error: 'فشل في إنشاء الحساب');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      state = state.copyWith(isLoading: true);
      await _supabaseService.signOut();
      state = AuthState();
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  String _getErrorMessage(dynamic error) {
    if (error is AuthException) {
      switch (error.message) {
        case 'Invalid login credentials':
          return 'بيانات الدخول غير صحيحة';
        case 'Email not confirmed':
          return 'يرجى تأكيد البريد الإلكتروني';
        case 'Too many requests':
          return 'محاولات كثيرة، يرجى المحاولة لاحقاً';
        case 'User already registered':
          return 'المستخدم مسجل بالفعل';
        case 'Password should be at least 6 characters':
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        case 'Unable to validate email address: invalid format':
          return 'تنسيق البريد الإلكتروني غير صحيح';
        case 'Signup requires a valid password':
          return 'يرجى إدخال كلمة مرور صحيحة';
        default:
          return 'خطأ في المصادقة: ${error.message}';
      }
    }

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'خطأ في الاتصال بالإنترنت';
    }

    if (errorString.contains('timeout')) {
      return 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';
    }

    if (errorString.contains('duplicate') ||
        errorString.contains('already exists')) {
      return 'البريد الإلكتروني مستخدم بالفعل';
    }

    return 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
  }
}

// Providers
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService.instance;
});

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return AuthNotifier(supabaseService);
});

// Helper providers
final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final userRoleProvider = Provider<UserRole?>((ref) {
  return ref.watch(authProvider).user?.role;
});

final canEditProvider = Provider<bool>((ref) {
  final role = ref.watch(userRoleProvider);
  return role?.canEdit ?? false;
});

final canDeleteProvider = Provider<bool>((ref) {
  final role = ref.watch(userRoleProvider);
  return role?.canDelete ?? false;
});

final canAddProvider = Provider<bool>((ref) {
  final role = ref.watch(userRoleProvider);
  return role?.canAdd ?? false;
});
