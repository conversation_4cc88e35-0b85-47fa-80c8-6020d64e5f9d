import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/supabase_service.dart';

// App Auth State
class AppAuthState {
  final bool isLoading;
  final UserModel? user;
  final String? error;
  final bool isAuthenticated;

  AppAuthState({
    this.isLoading = false,
    this.user,
    this.error,
    this.isAuthenticated = false,
  });

  AppAuthState copyWith({
    bool? isLoading,
    UserModel? user,
    String? error,
    bool? isAuthenticated,
  }) {
    return AppAuthState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

// Auth Notifier
class AuthNotifier extends StateNotifier<AppAuthState> {
  final SupabaseService _supabaseService;

  AuthNotifier(this._supabaseService) : super(AppAuthState()) {
    _init();
  }

  void _init() {
    // Listen to auth state changes
    _supabaseService.authStateChanges.listen((authState) {
      _handleAuthStateChange(authState);
    });

    // Check current user
    _checkCurrentUser();
  }

  Future<void> _checkCurrentUser() async {
    final currentUser = _supabaseService.currentUser;
    if (currentUser != null) {
      await _loadUserProfile(currentUser.id);
    }
  }

  void _handleAuthStateChange(AuthState authState) {
    if (authState.event == AuthChangeEvent.signedIn) {
      final user = authState.session?.user;
      if (user != null) {
        _loadUserProfile(user.id);
      }
    } else if (authState.event == AuthChangeEvent.signedOut) {
      state = AppAuthState();
    }
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final userProfile = await _supabaseService.getUserProfile(userId);

      if (userProfile != null) {
        state = state.copyWith(
          isLoading: false,
          user: userProfile,
          isAuthenticated: true,
        );
      } else {
        // Create default user profile if not exists
        final currentUser = _supabaseService.currentUser;
        if (currentUser != null) {
          final newProfile = await _supabaseService.createUserProfile(
            userId: currentUser.id,
            username:
                currentUser.email?.split('@')[0] ??
                'user${DateTime.now().millisecondsSinceEpoch}',
            role: UserRole.user, // Default role
          );

          state = state.copyWith(
            isLoading: false,
            user: newProfile,
            isAuthenticated: true,
          );
        }
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<bool> signIn({
    required String username,
    required String password,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _supabaseService.signInWithUsername(
        username: username,
        password: password,
      );

      if (response.user != null) {
        await _loadUserProfile(response.user!.id);
        return true;
      } else {
        state = state.copyWith(isLoading: false, error: 'فشل في تسجيل الدخول');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      state = state.copyWith(isLoading: true);
      await _supabaseService.signOut();
      state = AppAuthState();
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  String _getErrorMessage(dynamic error) {
    if (error is AuthException) {
      switch (error.message) {
        case 'Invalid login credentials':
          return 'بيانات الدخول غير صحيحة';
        case 'Email not confirmed':
          return 'يرجى تأكيد البريد الإلكتروني';
        case 'Too many requests':
          return 'محاولات كثيرة، يرجى المحاولة لاحقاً';
        default:
          return 'خطأ في المصادقة: ${error.message}';
      }
    }
    return 'حدث خطأ غير متوقع';
  }
}

// Providers
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService.instance;
});

final authProvider = StateNotifierProvider<AuthNotifier, AppAuthState>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return AuthNotifier(supabaseService);
});

// Helper providers
final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final userRoleProvider = Provider<UserRole?>((ref) {
  return ref.watch(authProvider).user?.role;
});

final canEditProvider = Provider<bool>((ref) {
  final role = ref.watch(userRoleProvider);
  return role?.canEdit ?? false;
});

final canDeleteProvider = Provider<bool>((ref) {
  final role = ref.watch(userRoleProvider);
  return role?.canDelete ?? false;
});

final canAddProvider = Provider<bool>((ref) {
  final role = ref.watch(userRoleProvider);
  return role?.canAdd ?? false;
});
