import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/user_model.dart';
import '../services/supabase_service.dart';
import 'auth_provider.dart';

// Users State
class UsersState {
  final bool isLoading;
  final List<UserModel> users;
  final String? error;

  UsersState({this.isLoading = false, this.users = const [], this.error});

  UsersState copyWith({
    bool? isLoading,
    List<UserModel>? users,
    String? error,
  }) {
    return UsersState(
      isLoading: isLoading ?? this.isLoading,
      users: users ?? this.users,
      error: error,
    );
  }
}

// Users Notifier
class UsersNotifier extends StateNotifier<UsersState> {
  final SupabaseService _supabaseService;

  UsersNotifier(this._supabaseService) : super(UsersState());

  Future<void> loadUsers() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final users = await _supabaseService.getAllUsers();

      state = state.copyWith(isLoading: false, users: users);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
    }
  }

  Future<UserModel?> getUserById(String userId) async {
    try {
      return await _supabaseService.getUserById(userId);
    } catch (e) {
      state = state.copyWith(error: _getErrorMessage(e));
      return null;
    }
  }

  Future<bool> createUser({
    required String username,
    required String email,
    required String password,
    required UserRole role,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final newUser = await _supabaseService.createUser(
        username: username,
        email: email,
        password: password,
        role: role,
      );

      // Add to local state
      final updatedUsers = [...state.users, newUser];

      state = state.copyWith(isLoading: false, users: updatedUsers);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<bool> updateUser(
    String userId, {
    String? username,
    String? email,
    UserRole? role,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _supabaseService.updateUser(
        userId,
        username: username,
        email: email,
        role: role,
      );

      // Update local state
      final updatedUsers = state.users.map((user) {
        if (user.id == userId) {
          return user.copyWith(
            username: username ?? user.username,
            email: email ?? user.email,
            role: role ?? user.role,
          );
        }
        return user;
      }).toList();

      state = state.copyWith(isLoading: false, users: updatedUsers);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<bool> updateUserRole(String userId, UserRole newRole) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _supabaseService.updateUserRole(userId, newRole);

      // Update local state
      final updatedUsers = state.users.map((user) {
        if (user.id == userId) {
          return user.copyWith(role: newRole);
        }
        return user;
      }).toList();

      state = state.copyWith(isLoading: false, users: updatedUsers);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<bool> deleteUser(String userId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _supabaseService.deleteUser(userId);

      // Update local state
      final updatedUsers = state.users
          .where((user) => user.id != userId)
          .toList();

      state = state.copyWith(isLoading: false, users: updatedUsers);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  String _getErrorMessage(dynamic error) {
    if (error.toString().contains('permission')) {
      return 'ليس لديك صلاحية لتنفيذ هذا الإجراء';
    }
    if (error.toString().contains('network')) {
      return 'خطأ في الاتصال بالإنترنت';
    }
    if (error.toString().contains('not found')) {
      return 'المستخدم غير موجود';
    }
    return 'حدث خطأ غير متوقع';
  }
}

// Providers
final usersProvider = StateNotifierProvider<UsersNotifier, UsersState>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return UsersNotifier(supabaseService);
});

// Helper providers
final usersListProvider = Provider<List<UserModel>>((ref) {
  return ref.watch(usersProvider).users;
});

final usersLoadingProvider = Provider<bool>((ref) {
  return ref.watch(usersProvider).isLoading;
});

final usersErrorProvider = Provider<String?>((ref) {
  return ref.watch(usersProvider).error;
});

final adminUsersProvider = Provider<List<UserModel>>((ref) {
  return ref
      .watch(usersProvider)
      .users
      .where((user) => user.role == UserRole.admin)
      .toList();
});

final regularUsersProvider = Provider<List<UserModel>>((ref) {
  return ref
      .watch(usersProvider)
      .users
      .where((user) => user.role == UserRole.user)
      .toList();
});

final usersCountProvider = Provider<int>((ref) {
  return ref.watch(usersProvider).users.length;
});

final adminCountProvider = Provider<int>((ref) {
  return ref.watch(adminUsersProvider).length;
});

final regularUsersCountProvider = Provider<int>((ref) {
  return ref.watch(regularUsersProvider).length;
});
