import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_model.dart';
import '../services/supabase_service.dart';
import 'auth_provider.dart';

// Users State
class UsersState {
  final bool isLoading;
  final List<UserModel> users;
  final String? error;
  final String searchQuery;
  final int totalCount;
  final int currentPage;
  final int itemsPerPage;
  final bool hasMore;

  UsersState({
    this.isLoading = false,
    this.users = const [],
    this.error,
    this.searchQuery = '',
    this.totalCount = 0,
    this.currentPage = 0,
    this.itemsPerPage = 20,
    this.hasMore = true,
  });

  UsersState copyWith({
    bool? isLoading,
    List<UserModel>? users,
    String? error,
    String? searchQuery,
    int? totalCount,
    int? currentPage,
    int? itemsPerPage,
    bool? hasMore,
  }) {
    return UsersState(
      isLoading: isLoading ?? this.isLoading,
      users: users ?? this.users,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      totalCount: totalCount ?? this.totalCount,
      currentPage: currentPage ?? this.currentPage,
      itemsPerPage: itemsPerPage ?? this.itemsPerPage,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

// Users Notifier
class UsersNotifier extends StateNotifier<UsersState> {
  final SupabaseService _supabaseService;

  UsersNotifier(this._supabaseService) : super(UsersState());

  Future<void> loadUsers({bool refresh = false}) async {
    if (state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true, error: null);

      final offset = state.currentPage * state.itemsPerPage;

      final users = await _supabaseService.getUsers(
        searchQuery: state.searchQuery.isEmpty ? null : state.searchQuery,
        limit: state.itemsPerPage,
        offset: offset,
      );

      final totalCount = await _supabaseService.getUsersCount(
        searchQuery: state.searchQuery.isEmpty ? null : state.searchQuery,
      );

      final allUsers = refresh ? users : [...state.users, ...users];
      final hasMore = allUsers.length < totalCount;

      state = state.copyWith(
        isLoading: false,
        users: allUsers,
        totalCount: totalCount,
        currentPage: refresh ? 0 : state.currentPage + 1,
        hasMore: hasMore,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
    }
  }

  Future<void> searchUsers(String query) async {
    state = state.copyWith(
      searchQuery: query,
      currentPage: 0,
      users: [],
      hasMore: true,
    );
    await loadUsers();
  }

  Future<void> loadMoreUsers() async {
    if (!state.hasMore || state.isLoading) return;
    await loadUsers();
  }

  Future<bool> createUser({
    required String email,
    required String password,
    String? phone,
    required UserRole role,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final newUser = await _supabaseService.createUserByAdmin(
        email: email,
        password: password,
        phone: phone,
        role: role,
      );

      final updatedUsers = <UserModel>[newUser, ...state.users];

      state = state.copyWith(
        isLoading: false,
        users: updatedUsers,
        totalCount: state.totalCount + 1,
      );

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<bool> updateUser(UserModel user) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final updatedUser = await _supabaseService.updateUserProfile(
        userId: user.id,
        email: user.email,
        phone: user.phone,
        role: user.role,
      );

      final updatedUsers = state.users.map((u) {
        return u.id == updatedUser.id ? updatedUser : u;
      }).toList();

      state = state.copyWith(isLoading: false, users: updatedUsers);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<bool> deleteUser(String userId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _supabaseService.deleteUser(userId);

      final updatedUsers = state.users.where((u) => u.id != userId).toList();

      state = state.copyWith(
        isLoading: false,
        users: updatedUsers,
        totalCount: state.totalCount - 1,
      );

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<bool> changeUserRole(String userId, UserRole newRole) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final updatedUser = await _supabaseService.updateUserProfile(
        userId: userId,
        role: newRole,
      );

      final updatedUsers = state.users.map((u) {
        return u.id == updatedUser.id ? updatedUser : u;
      }).toList();

      state = state.copyWith(isLoading: false, users: updatedUsers);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  String _getErrorMessage(dynamic error) {
    if (error.toString().contains('duplicate key')) {
      return 'البريد الإلكتروني موجود بالفعل';
    }
    if (error.toString().contains('network')) {
      return 'خطأ في الاتصال بالإنترنت';
    }
    if (error.toString().contains('permission')) {
      return 'ليس لديك صلاحية لتنفيذ هذا الإجراء';
    }
    return 'حدث خطأ غير متوقع';
  }
}

// Providers
final usersProvider = StateNotifierProvider<UsersNotifier, UsersState>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return UsersNotifier(supabaseService);
});

// Helper providers
final usersListProvider = Provider<List<UserModel>>((ref) {
  return ref.watch(usersProvider).users;
});

final usersLoadingProvider = Provider<bool>((ref) {
  return ref.watch(usersProvider).isLoading;
});

final usersErrorProvider = Provider<String?>((ref) {
  return ref.watch(usersProvider).error;
});

final usersSearchQueryProvider = Provider<String>((ref) {
  return ref.watch(usersProvider).searchQuery;
});

final usersTotalCountProvider = Provider<int>((ref) {
  return ref.watch(usersProvider).totalCount;
});

final adminUsersCountProvider = Provider<int>((ref) {
  final users = ref.watch(usersListProvider);
  return users.where((user) => user.role == UserRole.admin).length;
});

final regularUsersCountProvider = Provider<int>((ref) {
  final users = ref.watch(usersListProvider);
  return users.where((user) => user.role == UserRole.user).length;
});
