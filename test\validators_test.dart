import 'package:flutter_test/flutter_test.dart';
import 'package:intakhapat/utils/validators.dart';

void main() {
  group('National ID Validation Tests', () {
    group('Simple National ID Validation', () {
      test('should accept valid 14-digit national ID', () {
        expect(Validators.nationalIdSimple('29502043452645'), isNull);
        expect(Validators.nationalIdSimple('30112251234567'), isNull);
        expect(Validators.nationalIdSimple('12345678901234'), isNull);
      });

      test('should reject empty or null national ID', () {
        expect(Validators.nationalIdSimple(''), isNotNull);
        expect(Validators.nationalIdSimple(null), isNotNull);
      });

      test('should reject national ID with wrong length', () {
        expect(
          Validators.nationalIdSimple('123456789'),
          isNotNull,
        ); // Too short
        expect(
          Validators.nationalIdSimple('123456789012345'),
          isNotNull,
        ); // Too long
      });

      test('should reject national ID with non-digits', () {
        expect(Validators.nationalIdSimple('1234567890123a'), isNotNull);
        expect(Validators.nationalIdSimple('12345678901-34'), isNotNull);
        expect(Validators.nationalIdSimple('12345 67890123'), isNotNull);
      });

      test('should accept national ID with formatting that gets cleaned', () {
        expect(Validators.nationalIdSimple('295-02-04-3452645'), isNull);
        expect(Validators.nationalIdSimple('295 02 04 3452645'), isNull);
      });
    });

    group('Advanced National ID Validation (Now uses simple validation)', () {
      test('should accept any valid 14-digit national ID', () {
        expect(Validators.nationalId('29502043452645'), isNull);
        expect(Validators.nationalId('30112251234567'), isNull);
        expect(Validators.nationalId('25060112345678'), isNull);
      });

      test('should reject national ID with wrong length', () {
        expect(Validators.nationalId('123456789'), isNotNull); // Too short
        expect(Validators.nationalId('123456789012345'), isNotNull); // Too long
      });

      test('should reject national ID with non-digits', () {
        expect(Validators.nationalId('1234567890123a'), isNotNull);
        expect(Validators.nationalId(''), isNotNull);
        expect(Validators.nationalId(null), isNotNull);
      });

      test('should accept formatted national IDs', () {
        expect(Validators.nationalId('295-02-04-3452645'), isNull);
        expect(Validators.nationalId('295 02 04 3452645'), isNull);
      });
    });

    group('Real-world Examples', () {
      test('should handle the reported problematic national ID', () {
        // The national ID from the user's screenshot
        final nationalId = '29502043452645';

        // Should pass simple validation
        expect(Validators.nationalIdSimple(nationalId), isNull);

        // Should also pass advanced validation (1929-02-04)
        expect(Validators.nationalId(nationalId), isNull);
      });

      test('should handle formatted national IDs', () {
        final formattedId = '295-02-04-3452645';

        // Should pass simple validation
        expect(Validators.nationalIdSimple(formattedId), isNull);

        // Should also pass advanced validation
        expect(Validators.nationalId(formattedId), isNull);
      });

      test('should handle various birth years', () {
        // Different centuries
        expect(Validators.nationalIdSimple('19502043452645'), isNull); // 1919
        expect(Validators.nationalIdSimple('29502043452645'), isNull); // 1929
        expect(Validators.nationalIdSimple('39502043452645'), isNull); // 2039

        // Advanced validation should also work
        expect(Validators.nationalId('19502043452645'), isNull); // 1919
        expect(Validators.nationalId('29502043452645'), isNull); // 1929
        expect(Validators.nationalId('39502043452645'), isNull); // 2039
      });
    });
  });

  group('Other Validators', () {
    test('required validator should work', () {
      expect(Validators.required(''), isNotNull);
      expect(Validators.required(null), isNotNull);
      expect(Validators.required('   '), isNotNull);
      expect(Validators.required('test'), isNull);
    });

    test('email validator should work', () {
      expect(Validators.email('<EMAIL>'), isNull);
      expect(Validators.email('invalid-email'), isNotNull);
      expect(Validators.email(''), isNotNull);
    });

    test('committee location validator should work', () {
      expect(Validators.committeeLocation('مدرسة الشهيد'), isNull);
      expect(Validators.committeeLocation('ab'), isNotNull); // Too short
      expect(Validators.committeeLocation(''), isNotNull);
    });
  });
}
