import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../models/user_model.dart';
import '../providers/users_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/user_card.dart';
import '../widgets/user_form_dialog.dart';
import '../widgets/advanced_search_bar.dart';
import '../widgets/floating_action_menu.dart';
import '../config/app_router.dart';

class UsersManagementScreen extends ConsumerStatefulWidget {
  const UsersManagementScreen({super.key});

  @override
  ConsumerState<UsersManagementScreen> createState() =>
      _UsersManagementScreenState();
}

class _UsersManagementScreenState extends ConsumerState<UsersManagementScreen> {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load users when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(usersProvider.notifier).loadUsers(refresh: true);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      ref.read(usersProvider.notifier).loadMoreUsers();
    }
  }

  Future<void> _onRefresh() async {
    await ref.read(usersProvider.notifier).loadUsers(refresh: true);
  }

  void _onSearch(String query, dynamic filter) {
    ref.read(usersProvider.notifier).searchUsers(query);
  }

  void _showUserForm({UserModel? user}) {
    showDialog(
      context: context,
      builder: (context) => UserFormDialog(user: user),
    );
  }

  Future<void> _confirmDeleteUser(UserModel user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف المستخدم "${user.email}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(usersProvider.notifier)
          .deleteUser(user.id);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف المستخدم بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _changeUserRole(UserModel user) async {
    final newRole = user.role == UserRole.admin
        ? UserRole.user
        : UserRole.admin;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير الصلاحية'),
        content: Text(
          'هل تريد تغيير صلاحية "${user.email}" إلى "${newRole.displayName}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(usersProvider.notifier)
          .changeUserRole(user.id, newRole);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تغيير الصلاحية إلى "${newRole.displayName}" بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final usersState = ref.watch(usersProvider);
    final currentUser = ref.watch(currentUserProvider);
    final totalUsers = ref.watch(usersTotalCountProvider);
    final adminCount = ref.watch(adminUsersCountProvider);
    final userCount = ref.watch(regularUsersCountProvider);

    // Check if current user is admin
    final isAdmin = currentUser?.role == UserRole.admin;
    if (!isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('إدارة المستخدمين'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.safePop(),
          ),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.lock, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'ليس لديك صلاحية للوصول إلى هذه الصفحة',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
              SizedBox(height: 8),
              Text(
                'يجب أن تكون مسؤولاً لإدارة المستخدمين',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المستخدمين'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.safePop(),
        ),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _onRefresh),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: Column(
          children: [
            // Search Bar
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: AdvancedSearchBar(
                controller: _searchController,
                onSearch: _onSearch,
                hint: 'البحث بالبريد الإلكتروني أو رقم الهاتف',
              ),
            ),

            // Stats Row
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'إجمالي المستخدمين',
                      totalUsers.toString(),
                      FontAwesomeIcons.users,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'المسؤولين',
                      adminCount.toString(),
                      FontAwesomeIcons.crown,
                      Colors.purple,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'المستخدمين',
                      userCount.toString(),
                      FontAwesomeIcons.user,
                      Colors.green,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Users List
            Expanded(
              child: usersState.users.isEmpty && !usersState.isLoading
                  ? _buildEmptyState()
                  : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount:
                          usersState.users.length +
                          (usersState.hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index >= usersState.users.length) {
                          return Container(
                            padding: const EdgeInsets.all(16.0),
                            child: const Center(
                              child: Column(
                                children: [
                                  SpinKitThreeBounce(
                                    color: Colors.blue,
                                    size: 20,
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'جاري تحميل المزيد...',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        final user = usersState.users[index];
                        final isCurrentUser = user.id == currentUser?.id;

                        return UserCard(
                          user: user,
                          isCurrentUser: isCurrentUser,
                          onEdit: () => _showUserForm(user: user),
                          onDelete: isCurrentUser
                              ? null
                              : () => _confirmDeleteUser(user),
                          onChangeRole: isCurrentUser
                              ? null
                              : () => _changeUserRole(user),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionMenu(
        onAddPressed: () => _showUserForm(),
        addButtonText: 'إضافة مستخدم',
        addButtonIcon: FontAwesomeIcons.userPlus,
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(FontAwesomeIcons.users, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد مستخدمين',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة مستخدم جديد',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }
}
