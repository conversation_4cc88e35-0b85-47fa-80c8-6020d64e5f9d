import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:intakhapat/main.dart';

void main() {
  group('Intakhabat App Tests', () {
    testWidgets('App should start without crashing', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(ProviderScope(child: MyApp()));

      // Verify that the app starts without throwing an exception
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Login screen should be displayed initially', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(ProviderScope(child: MyApp()));

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Verify that login screen elements are present
      expect(find.text('نظام إدارة الانتخابات'), findsOneWidget);
      expect(find.text('تسجيل الدخول للمتابعة'), findsOneWidget);
    });

    testWidgets('Login form validation should work', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(ProviderScope(child: MyApp()));

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Find the login button and tap it without entering credentials
      final loginButton = find.text('تسجيل الدخول');
      expect(loginButton, findsOneWidget);

      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // Verify that validation errors are shown
      expect(
        find.text('يرجى إدخال البريد الإلكتروني أو رقم الهاتف'),
        findsOneWidget,
      );
      expect(find.text('يرجى إدخال كلمة المرور'), findsOneWidget);
    });
  });

  group('Validation Tests', () {
    test('Email validation should work correctly', () {
      // Test valid emails
      expect(_isValidEmail('<EMAIL>'), isTrue);
      expect(_isValidEmail('<EMAIL>'), isTrue);
      expect(_isValidEmail('<EMAIL>'), isTrue);

      // Test invalid emails
      expect(_isValidEmail('invalid-email'), isFalse);
      expect(_isValidEmail('test@'), isFalse);
      expect(_isValidEmail('@example.com'), isFalse);
      expect(_isValidEmail(''), isFalse);
    });

    test('Phone validation should work correctly', () {
      // Test valid phone numbers
      expect(_isValidPhone('01234567890'), isTrue);
      expect(_isValidPhone('+201234567890'), isTrue);
      expect(_isValidPhone('(*************'), isTrue);

      // Test invalid phone numbers
      expect(_isValidPhone('123'), isFalse);
      expect(_isValidPhone('abcdefghij'), isFalse);
      expect(_isValidPhone(''), isFalse);
    });

    test('National ID validation should work correctly', () {
      // Test valid national IDs (14 digits)
      expect(_isValidNationalId('12345678901234'), isTrue);
      expect(_isValidNationalId('98765432109876'), isTrue);

      // Test invalid national IDs
      expect(_isValidNationalId('123456789'), isFalse); // Too short
      expect(_isValidNationalId('123456789012345'), isFalse); // Too long
      expect(_isValidNationalId('1234567890123a'), isFalse); // Contains letter
      expect(_isValidNationalId(''), isFalse); // Empty
    });
  });
}

// Helper functions for validation tests
bool _isValidEmail(String email) {
  if (email.isEmpty) return false;
  return RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  ).hasMatch(email);
}

bool _isValidPhone(String phone) {
  if (phone.isEmpty || phone.length < 10) return false;
  final phoneRegex = RegExp(r'^[\d\+\-\(\)\s]+$');
  return phoneRegex.hasMatch(phone);
}

bool _isValidNationalId(String nationalId) {
  if (nationalId.isEmpty || nationalId.length != 14) return false;
  return RegExp(r'^\d{14}$').hasMatch(nationalId);
}
