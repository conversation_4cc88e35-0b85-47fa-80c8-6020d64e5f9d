import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class FloatingActionMenu extends StatelessWidget {
  final VoidCallback onAddPressed;

  const FloatingActionMenu({super.key, required this.onAddPressed});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: onAddPressed,
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      icon: const Icon(FontAwesomeIcons.plus, size: 18),
      label: const Text(
        'إضافة مسجل',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      elevation: 4,
    );
  }
}
