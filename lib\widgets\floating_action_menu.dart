import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class FloatingActionMenu extends StatelessWidget {
  final VoidCallback onAddPressed;
  final String? addButtonText;
  final IconData? addButtonIcon;

  const FloatingActionMenu({
    super.key,
    required this.onAddPressed,
    this.addButtonText,
    this.addButtonIcon,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: onAddPressed,
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      icon: Icon(addButtonIcon ?? FontAwesomeIcons.plus, size: 18),
      label: Text(
        addButtonText ?? 'إضافة مسجل',
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      elevation: 4,
    );
  }
}
