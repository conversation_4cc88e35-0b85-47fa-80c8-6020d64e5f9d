import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../models/voter_model.dart';
import '../providers/voters_provider.dart';
import '../utils/validators.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import '../widgets/committee_dropdown.dart';

class VoterFormScreen extends ConsumerStatefulWidget {
  final VoterModel? voter;

  const VoterFormScreen({super.key, this.voter});

  @override
  ConsumerState<VoterFormScreen> createState() => _VoterFormScreenState();
}

class _VoterFormScreenState extends ConsumerState<VoterFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _committeeLocationController = TextEditingController();

  bool get isEditing => widget.voter != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _nameController.text = widget.voter!.name;
      _nationalIdController.text = widget.voter!.nationalId;
      _committeeLocationController.text = widget.voter!.committeeLocation;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _nationalIdController.dispose();
    _committeeLocationController.dispose();
    super.dispose();
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    final voter = VoterModel(
      id: isEditing ? widget.voter!.id : null,
      name: _nameController.text.trim(),
      nationalId: _nationalIdController.text.trim(),
      committeeLocation: _committeeLocationController.text.trim(),
    );

    bool success;
    if (isEditing) {
      success = await ref.read(votersProvider.notifier).updateVoter(voter);
    } else {
      success = await ref.read(votersProvider.notifier).addVoter(voter);
    }

    if (success && mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEditing ? 'تم تحديث البيانات بنجاح' : 'تم إضافة البيانات بنجاح',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final votersState = ref.watch(votersProvider);

    // Listen to voters errors
    ref.listen<VotersState>(votersProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
        // Clear error after showing
        Future.delayed(const Duration(seconds: 3), () {
          ref.read(votersProvider.notifier).clearError();
        });
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'تعديل البيانات' : 'إضافة مسجل جديد'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (votersState.isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SpinKitThreeBounce(color: Colors.white, size: 20),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Form Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    Icon(
                      isEditing ? Icons.edit : Icons.person_add,
                      size: 48,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      isEditing ? 'تعديل بيانات المسجل' : 'إضافة مسجل جديد',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'يرجى ملء جميع الحقول المطلوبة',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Name Field
              CustomTextField(
                controller: _nameController,
                label: 'الاسم الكامل *',
                hint: 'أدخل الاسم الكامل',
                prefixIcon: Icons.person_outline,
                validator: Validators.required,
                enabled: !votersState.isLoading,
                textCapitalization: TextCapitalization.words,
              ),

              const SizedBox(height: 20),

              // National ID Field
              CustomTextField(
                controller: _nationalIdController,
                label: 'الرقم القومي *',
                hint: 'أدخل الرقم القومي (14 رقم)',
                prefixIcon: Icons.credit_card,
                keyboardType: TextInputType.number,
                validator: Validators.nationalId,
                enabled: !votersState.isLoading,
                maxLength: 14,
              ),

              const SizedBox(height: 20),

              // Committee Location Field
              CommitteeDropdown(
                controller: _committeeLocationController,
                enabled: !votersState.isLoading,
              ),

              const SizedBox(height: 40),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: votersState.isLoading
                          ? null
                          : () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: BorderSide(color: Theme.of(context).primaryColor),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: CustomButton(
                      onPressed: votersState.isLoading ? null : _handleSave,
                      child: votersState.isLoading
                          ? const SpinKitThreeBounce(
                              color: Colors.white,
                              size: 20,
                            )
                          : Text(
                              isEditing ? 'حفظ التعديلات' : 'إضافة المسجل',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Help Text
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.amber.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.amber.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.amber[700],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'ملاحظات مهمة',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.amber[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• الرقم القومي يجب أن يكون 14 رقم\n'
                      '• لا يمكن تكرار الرقم القومي\n'
                      '• جميع الحقول مطلوبة',
                      style: TextStyle(color: Colors.amber[600], fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
