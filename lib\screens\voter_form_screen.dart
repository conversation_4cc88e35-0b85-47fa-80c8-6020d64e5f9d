import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:go_router/go_router.dart';

import '../models/voter_model.dart';
import '../providers/voters_provider.dart';
import '../utils/validators.dart';
import '../utils/responsive_helper.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import '../widgets/committee_autocomplete.dart';
import '../config/app_router.dart';

class VoterFormScreen extends ConsumerStatefulWidget {
  final String? voterId;

  const VoterFormScreen({super.key, this.voterId});

  @override
  ConsumerState<VoterFormScreen> createState() => _VoterFormScreenState();
}

class _VoterFormScreenState extends ConsumerState<VoterFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _committeeLocationController = TextEditingController();

  VoterModel? _currentVoter;
  bool _isLoading = false;

  bool get isEditing => widget.voterId != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _loadVoter();
    }
  }

  Future<void> _loadVoter() async {
    if (widget.voterId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get voter from the current list or fetch from database
      final votersState = ref.read(votersProvider);
      _currentVoter = votersState.voters.firstWhere(
        (voter) => voter.id == widget.voterId,
        orElse: () => throw Exception('Voter not found'),
      );

      _nameController.text = _currentVoter!.name;
      _nationalIdController.text = _formatNationalId(_currentVoter!.nationalId);
      _committeeLocationController.text = _currentVoter!.committeeLocation;
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في تحميل بيانات المسجل'),
            backgroundColor: Colors.red,
          ),
        );
        context.safePop();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _nationalIdController.dispose();
    _committeeLocationController.dispose();
    super.dispose();
  }

  String _formatNationalId(String value) {
    // Remove any non-digit characters
    final digitsOnly = value.replaceAll(RegExp(r'\D'), '');

    // Limit to 14 digits
    final limited = digitsOnly.length > 14
        ? digitsOnly.substring(0, 14)
        : digitsOnly;

    // Add formatting: XXX-XX-XX-XXXXX
    if (limited.length <= 3) {
      return limited;
    } else if (limited.length <= 5) {
      return '${limited.substring(0, 3)}-${limited.substring(3)}';
    } else if (limited.length <= 7) {
      return '${limited.substring(0, 3)}-${limited.substring(3, 5)}-${limited.substring(5)}';
    } else {
      return '${limited.substring(0, 3)}-${limited.substring(3, 5)}-${limited.substring(5, 7)}-${limited.substring(7)}';
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    // Clean national ID by removing formatting characters
    final cleanNationalId = _nationalIdController.text.replaceAll(
      RegExp(r'[\s\-]'),
      '',
    );

    final voter = VoterModel(
      id: isEditing ? _currentVoter!.id : null,
      name: _nameController.text.trim(),
      nationalId: cleanNationalId,
      committeeLocation: _committeeLocationController.text.trim(),
    );

    bool success;
    if (isEditing) {
      success = await ref.read(votersProvider.notifier).updateVoter(voter);
    } else {
      success = await ref.read(votersProvider.notifier).addVoter(voter);
    }

    if (success && mounted) {
      // Safe navigation back
      context.safePop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEditing ? 'تم تحديث البيانات بنجاح' : 'تم إضافة البيانات بنجاح',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final votersState = ref.watch(votersProvider);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('تحميل البيانات...')),
        body: const Center(
          child: SpinKitThreeBounce(color: Colors.blue, size: 30),
        ),
      );
    }

    // Listen to voters errors
    ref.listen<VotersState>(votersProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
        // Clear error after showing
        Future.delayed(const Duration(seconds: 3), () {
          ref.read(votersProvider.notifier).clearError();
        });
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'تعديل البيانات' : 'إضافة مسجل جديد'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (votersState.isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SpinKitThreeBounce(color: Colors.white, size: 20),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ResponsiveHelper.buildResponsiveContainer(
          context: context,
          maxWidth: ResponsiveHelper.isMobile(context) ? double.infinity : 600,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Form Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        isEditing ? Icons.edit : Icons.person_add,
                        size: 48,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        isEditing ? 'تعديل بيانات المسجل' : 'إضافة مسجل جديد',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'يرجى ملء جميع الحقول المطلوبة',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Name Field
                CustomTextField(
                  controller: _nameController,
                  label: 'الاسم الكامل *',
                  hint: 'أدخل الاسم الكامل',
                  prefixIcon: Icons.person_outline,
                  validator: Validators.combine([
                    Validators.required,
                    Validators.name,
                    Validators.minLength(2, 'الاسم'),
                    Validators.maxLength(100, 'الاسم'),
                  ]),
                  enabled: !votersState.isLoading,
                  textCapitalization: TextCapitalization.words,
                ),

                const SizedBox(height: 20),

                // National ID Field
                CustomTextField(
                  controller: _nationalIdController,
                  label: 'الرقم القومي *',
                  hint: 'أدخل 14 رقم فقط (مثال: 29502043452645)',
                  prefixIcon: Icons.credit_card,
                  keyboardType: TextInputType.number,
                  validator: Validators.combine([
                    Validators.required,
                    Validators.nationalIdSimple, // استخدام التحقق المبسط
                  ]),
                  enabled: !votersState.isLoading,
                  maxLength: 17, // Allow for formatting characters
                  onChanged: (value) {
                    // Format national ID as user types
                    final formatted = _formatNationalId(value);
                    if (formatted != value) {
                      _nationalIdController.value = TextEditingValue(
                        text: formatted,
                        selection: TextSelection.collapsed(
                          offset: formatted.length,
                        ),
                      );
                    }
                  },
                ),

                const SizedBox(height: 20),

                // Committee Location Field
                CommitteeAutocomplete(
                  controller: _committeeLocationController,
                  enabled: !votersState.isLoading,
                  validator: Validators.committeeLocation,
                ),

                const SizedBox(height: 40),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: votersState.isLoading
                            ? null
                            : () => context.safePop(),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          side: BorderSide(
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        child: const Text(
                          'إلغاء',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 2,
                      child: CustomButton(
                        onPressed: votersState.isLoading ? null : _handleSave,
                        child: votersState.isLoading
                            ? const SpinKitThreeBounce(
                                color: Colors.white,
                                size: 20,
                              )
                            : Text(
                                isEditing ? 'حفظ التعديلات' : 'إضافة المسجل',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Help Text
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.amber.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.amber[700],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'ملاحظات مهمة',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.amber[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• الرقم القومي يجب أن يكون 14 رقم فقط\n'
                        '• يمكن إدخال الأرقام فقط (بدون مسافات أو شرطات)\n'
                        '• لا يمكن تكرار الرقم القومي\n'
                        '• جميع الحقول مطلوبة',
                        style: TextStyle(
                          color: Colors.amber[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
