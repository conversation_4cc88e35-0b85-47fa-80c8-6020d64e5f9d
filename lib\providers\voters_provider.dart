import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/voter_model.dart';
import '../services/supabase_service.dart';
import 'auth_provider.dart';

// Voters State
class VotersState {
  final bool isLoading;
  final List<VoterModel> voters;
  final String? error;
  final String searchQuery;
  final int totalCount;
  final int currentPage;
  final int itemsPerPage;
  final bool hasMore;

  VotersState({
    this.isLoading = false,
    this.voters = const [],
    this.error,
    this.searchQuery = '',
    this.totalCount = 0,
    this.currentPage = 0,
    this.itemsPerPage = 20,
    this.hasMore = true,
  });

  VotersState copyWith({
    bool? isLoading,
    List<VoterModel>? voters,
    String? error,
    String? searchQuery,
    int? totalCount,
    int? currentPage,
    int? itemsPerPage,
    bool? hasMore,
  }) {
    return VotersState(
      isLoading: isLoading ?? this.isLoading,
      voters: voters ?? this.voters,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      totalCount: totalCount ?? this.totalCount,
      currentPage: currentPage ?? this.currentPage,
      itemsPerPage: itemsPerPage ?? this.itemsPerPage,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

// Voters Notifier
class VotersNotifier extends StateNotifier<VotersState> {
  final SupabaseService _supabaseService;

  VotersNotifier(this._supabaseService) : super(VotersState());

  Future<void> loadVoters({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(currentPage: 0, voters: [], hasMore: true);
    }

    if (state.isLoading || !state.hasMore) return;

    try {
      state = state.copyWith(isLoading: true, error: null);

      final offset = state.currentPage * state.itemsPerPage;

      final voters = await _supabaseService.getVoters(
        searchQuery: state.searchQuery.isEmpty ? null : state.searchQuery,
        limit: state.itemsPerPage,
        offset: offset,
      );

      final totalCount = await _supabaseService.getVotersCount(
        searchQuery: state.searchQuery.isEmpty ? null : state.searchQuery,
      );

      final allVoters = refresh ? voters : [...state.voters, ...voters];
      final hasMore = allVoters.length < totalCount;

      state = state.copyWith(
        isLoading: false,
        voters: allVoters,
        totalCount: totalCount,
        currentPage: state.currentPage + 1,
        hasMore: hasMore,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
    }
  }

  Future<void> searchVoters(String query) async {
    state = state.copyWith(
      searchQuery: query,
      currentPage: 0,
      voters: [],
      hasMore: true,
    );
    await loadVoters();
  }

  Future<bool> addVoter(VoterModel voter) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Check if national ID already exists
      final exists = await _supabaseService.isNationalIdExists(
        voter.nationalId,
      );
      if (exists) {
        state = state.copyWith(
          isLoading: false,
          error: 'الرقم القومي موجود بالفعل',
        );
        return false;
      }

      final newVoter = await _supabaseService.createVoter(voter);

      state = state.copyWith(
        isLoading: false,
        voters: [newVoter, ...state.voters],
        totalCount: state.totalCount + 1,
      );

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<bool> updateVoter(VoterModel voter) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Check if national ID already exists (excluding current voter)
      final exists = await _supabaseService.isNationalIdExists(
        voter.nationalId,
        excludeId: voter.id,
      );
      if (exists) {
        state = state.copyWith(
          isLoading: false,
          error: 'الرقم القومي موجود بالفعل',
        );
        return false;
      }

      final updatedVoter = await _supabaseService.updateVoter(voter);

      final updatedVoters = state.voters.map((v) {
        return v.id == updatedVoter.id ? updatedVoter : v;
      }).toList();

      state = state.copyWith(isLoading: false, voters: updatedVoters);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  Future<bool> deleteVoter(String voterId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _supabaseService.deleteVoter(voterId);

      final updatedVoters = state.voters.where((v) => v.id != voterId).toList();

      state = state.copyWith(
        isLoading: false,
        voters: updatedVoters,
        totalCount: state.totalCount - 1,
      );

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: _getErrorMessage(e));
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  String _getErrorMessage(dynamic error) {
    if (error.toString().contains('duplicate key')) {
      return 'الرقم القومي موجود بالفعل';
    }
    if (error.toString().contains('network')) {
      return 'خطأ في الاتصال بالإنترنت';
    }
    return 'حدث خطأ غير متوقع';
  }
}

// Providers
final votersProvider = StateNotifierProvider<VotersNotifier, VotersState>((
  ref,
) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return VotersNotifier(supabaseService);
});

// Helper providers
final votersListProvider = Provider<List<VoterModel>>((ref) {
  return ref.watch(votersProvider).voters;
});

final votersLoadingProvider = Provider<bool>((ref) {
  return ref.watch(votersProvider).isLoading;
});

final votersErrorProvider = Provider<String?>((ref) {
  return ref.watch(votersProvider).error;
});

final votersSearchQueryProvider = Provider<String>((ref) {
  return ref.watch(votersProvider).searchQuery;
});

final votersTotalCountProvider = Provider<int>((ref) {
  return ref.watch(votersProvider).totalCount;
});

final votersHasMoreProvider = Provider<bool>((ref) {
  return ref.watch(votersProvider).hasMore;
});
