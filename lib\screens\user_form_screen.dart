import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../models/user_model.dart';
import '../providers/users_provider.dart';
import '../utils/validators.dart';
import '../widgets/custom_text_field.dart';
import '../utils/snackbar_helper.dart';

class UserFormScreen extends ConsumerStatefulWidget {
  final String? userId;

  const UserFormScreen({super.key, this.userId});

  @override
  ConsumerState<UserFormScreen> createState() => _UserFormScreenState();
}

class _UserFormScreenState extends ConsumerState<UserFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  UserRole _selectedRole = UserRole.user;
  bool _isLoading = false;
  bool _isEditing = false;
  UserModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.userId != null;
    if (_isEditing) {
      _loadUserData();
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    if (widget.userId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final user = await ref
          .read(usersProvider.notifier)
          .getUserById(widget.userId!);
      if (user != null && mounted) {
        setState(() {
          _currentUser = user;
          _usernameController.text = user.username;
          _selectedRole = user.role;
        });
      }
    } catch (e) {
      if (mounted) {
        SnackBarHelper.showError(context, 'خطأ في تحميل بيانات المستخدم');
        _safeNavigateBack();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _safeNavigateBack() {
    if (context.canPop()) {
      context.pop();
    } else {
      context.go('/permissions');
    }
  }

  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      bool success;

      if (_isEditing) {
        success = await ref
            .read(usersProvider.notifier)
            .updateUser(
              widget.userId!,
              username: _usernameController.text.trim(),
              role: _selectedRole,
            );
      } else {
        success = await ref
            .read(usersProvider.notifier)
            .createUser(
              username: _usernameController.text.trim(),
              password: _passwordController.text,
              role: _selectedRole,
            );
      }

      if (success && mounted) {
        SnackBarHelper.showSuccess(
          context,
          _isEditing ? 'تم تحديث المستخدم بنجاح' : 'تم إنشاء المستخدم بنجاح',
        );
        _safeNavigateBack();
      }
    } catch (e) {
      if (mounted) {
        // Get the error from the provider state
        final errorMessage =
            ref.read(usersProvider).error ??
            (_isEditing ? 'خطأ في تحديث المستخدم' : 'خطأ في إنشاء المستخدم');

        SnackBarHelper.showError(context, errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final usersState = ref.watch(usersProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading && _isEditing
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Header Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Icon(
                              _isEditing ? Icons.edit : Icons.person_add,
                              size: 48,
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _isEditing
                                  ? 'تعديل بيانات المستخدم'
                                  : 'إضافة مستخدم جديد',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            if (_isEditing && _currentUser != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                'المستخدم: ${_currentUser!.username}',
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(color: Colors.grey[600]),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Username Field
                    CustomTextField(
                      controller: _usernameController,
                      label: 'اسم المستخدم',
                      hint: 'أدخل اسم المستخدم',
                      prefixIcon: Icons.person,
                      validator: Validators.username,
                      enabled: !_isLoading,
                    ),

                    const SizedBox(height: 16),

                    // Password Field (only for new users)
                    if (!_isEditing) ...[
                      CustomTextField(
                        controller: _passwordController,
                        label: 'كلمة المرور',
                        hint: 'أدخل كلمة المرور',
                        prefixIcon: Icons.lock,
                        obscureText: true,
                        validator: Validators.password,
                        enabled: !_isLoading,
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Role Selection
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'الدور',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 12),
                            ...UserRole.values.map((role) {
                              return RadioListTile<UserRole>(
                                title: Text(role.displayName),
                                subtitle: Text(_getRoleDescription(role)),
                                value: role,
                                groupValue: _selectedRole,
                                onChanged: _isLoading
                                    ? null
                                    : (value) {
                                        setState(() {
                                          _selectedRole = value!;
                                        });
                                      },
                                activeColor: Theme.of(context).primaryColor,
                              );
                            }),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _isLoading ? null : _safeNavigateBack,
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              side: BorderSide(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            child: const Text('إلغاء'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _saveUser,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                : Text(_isEditing ? 'تحديث' : 'إضافة'),
                          ),
                        ),
                      ],
                    ),

                    // Error Display
                    if (usersState.error != null) ...[
                      const SizedBox(height: 16),
                      Card(
                        color: Colors.red[50],
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Row(
                            children: [
                              Icon(Icons.error, color: Colors.red[700]),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  usersState.error!,
                                  style: TextStyle(color: Colors.red[700]),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],

                    // Help Tips
                    if (!_isEditing) ...[
                      const SizedBox(height: 16),
                      Card(
                        color: Colors.blue[50],
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.info, color: Colors.blue[700]),
                                  const SizedBox(width: 8),
                                  Text(
                                    'نصائح مهمة',
                                    style: TextStyle(
                                      color: Colors.blue[700],
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '• كلمة المرور يجب أن تكون 6 أحرف على الأقل\n'
                                '• اسم المستخدم يجب أن يكون فريد\n'
                                '• اختر الدور المناسب للمستخدم\n'
                                '• المستخدم الجديد سيتمكن من تسجيل الدخول فوراً',
                                style: TextStyle(
                                  color: Colors.blue[600],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
    );
  }

  String _getRoleDescription(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'يمكنه إدارة جميع البيانات والمستخدمين';
      case UserRole.user:
        return 'يمكنه عرض البيانات فقط';
    }
  }
}
