# دليل نشر تطبيق إدارة الانتخابات

## حل مشكلة قاعدة البيانات الحالية

### المشكلة
ظهور خطأ "PostgresException(message: infinite recursion detected in policy for relation "user_profiles")"

### الحل السريع

1. **اذهب إلى Supabase Dashboard**
   - افتح [Supabase Dashboard](https://app.supabase.com)
   - اختر مشروعك

2. **افتح SQL Editor**
   - اذهب إلى SQL Editor من القائمة الجانبية

3. **تطبيق الإصلاحات**
   - انسخ محتوى ملف `database/fix_policies.sql`
   - الصقه في SQL Editor
   - اضغط "Run" لتنفيذ الكود

4. **إنشاء مستخدم Admin**
   ```sql
   -- استبدل البريد الإلكتروني بالبريد الذي تريد جعله admin
   SELECT public.make_user_admin('<EMAIL>');
   ```

5. **إعادة تشغيل التطبيق**
   - أعد تشغيل التطبيق
   - جرب تسجيل الدخول

## إعداد التطبيق للإنتاج

### 1. إعداد قاعدة البيانات

#### إنشاء مشروع Supabase جديد (إذا لزم الأمر)
1. اذهب إلى [Supabase](https://app.supabase.com)
2. أنشئ مشروع جديد
3. احفظ URL المشروع و API Key

#### تطبيق إعدادات قاعدة البيانات
1. نفذ ملف `database/supabase_setup.sql` في SQL Editor
2. نفذ ملف `database/fix_policies.sql` لإصلاح المشاكل
3. أنشئ مستخدم admin أول

### 2. إعداد التطبيق

#### تحديث إعدادات Supabase
قم بتحديث الملفات التالية بمعلومات مشروعك:

**lib/config/supabase_config.dart:**
```dart
class SupabaseConfig {
  static const String supabaseUrl = 'YOUR_SUPABASE_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
}
```

**lib/config/supabase_config_production.dart:**
```dart
class SupabaseConfigProduction {
  static String get supabaseUrl {
    const url = String.fromEnvironment('YOUR_SUPABASE_URL');
    // ... باقي الكود
  }
}
```

### 3. تشغيل التطبيق

#### للتطوير
```bash
flutter run
```

#### للإنتاج (Web)
```bash
flutter build web
```

#### للإنتاج (Android)
```bash
flutter build apk --release
```

#### للإنتاج (iOS)
```bash
flutter build ios --release
```

## الميزات المتاحة

### للمستخدم العادي
- تسجيل الدخول/الخروج
- عرض قائمة المسجلين
- البحث في البيانات
- عرض الإحصائيات الأساسية

### للمسؤول (Admin)
- جميع ميزات المستخدم العادي
- إضافة مسجلين جدد
- تعديل بيانات المسجلين
- حذف المسجلين
- إدارة أماكن اللجان
- عرض إحصائيات متقدمة
- تصدير البيانات (CSV/JSON)

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
1. تحقق من صحة URL و API Key
2. تأكد من أن المشروع نشط في Supabase
3. تحقق من إعدادات الشبكة

### خطأ في الصلاحيات
1. تأكد من تطبيق ملف `fix_policies.sql`
2. تحقق من أن المستخدم له صلاحيات admin
3. راجع سياسات Row Level Security

### خطأ في البيانات
1. تحقق من صحة البيانات المدخلة
2. تأكد من عدم تكرار الرقم القومي
3. راجع قيود قاعدة البيانات

## الأمان

### في الإنتاج
- استخدم HTTPS دائماً
- احم API Keys
- راجع الصلاحيات بانتظام
- قم بنسخ احتياطية دورية

### Row Level Security
- تم تفعيل RLS على جميع الجداول
- السياسات تحمي البيانات الحساسة
- التحقق من الصلاحيات في التطبيق والقاعدة

## الدعم

### في حالة المشاكل
1. راجع logs في Supabase Dashboard
2. تحقق من Console في المتصفح
3. راجع ملف `database/README.md` للتفاصيل

### الملفات المهمة
- `database/fix_policies.sql` - إصلاح مشاكل قاعدة البيانات
- `database/supabase_setup.sql` - إعداد قاعدة البيانات الأولي
- `database/README.md` - دليل قاعدة البيانات التفصيلي

## ملاحظات مهمة

1. **تأكد من تطبيق إصلاحات قاعدة البيانات قبل الاستخدام**
2. **أنشئ مستخدم admin أول قبل البدء**
3. **اختبر جميع الوظائف قبل النشر**
4. **قم بنسخ احتياطية دورية من البيانات**

---

تم تطوير هذا التطبيق باستخدام Flutter و Supabase مع التركيز على الأمان وسهولة الاستخدام.
