import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../models/user_model.dart';
import '../providers/auth_provider.dart';
import '../providers/users_provider.dart';
import '../widgets/loading_widget.dart' as widgets;
import '../utils/snackbar_helper.dart';

class PermissionsManagementScreen extends ConsumerStatefulWidget {
  const PermissionsManagementScreen({super.key});

  @override
  ConsumerState<PermissionsManagementScreen> createState() =>
      _PermissionsManagementScreenState();
}

class _PermissionsManagementScreenState
    extends ConsumerState<PermissionsManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Load users when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(usersProvider.notifier).loadUsers();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }

  List<UserModel> _getFilteredUsers(List<UserModel> users) {
    if (_searchQuery.isEmpty) {
      return users;
    }
    return users.where((user) {
      return user.username.toLowerCase().contains(_searchQuery) ||
          user.email.toLowerCase().contains(_searchQuery) ||
          user.role.displayName.toLowerCase().contains(_searchQuery);
    }).toList();
  }

  Future<void> _changeUserRole(UserModel user, UserRole newRole) async {
    final confirmed = await _showConfirmDialog(
      title: 'تغيير الصلاحيات',
      message:
          'هل أنت متأكد من تغيير صلاحيات "${user.username}" إلى "${newRole.displayName}"؟',
    );

    if (confirmed == true) {
      final success = await ref
          .read(usersProvider.notifier)
          .updateUserRole(user.id, newRole);

      if (success && mounted) {
        SnackBarHelper.showSuccess(
          context,
          'تم تغيير صلاحيات "${user.username}" بنجاح',
        );
      }
    }
  }

  Future<void> _deleteUser(UserModel user) async {
    final confirmed = await _showConfirmDialog(
      title: 'حذف المستخدم',
      message:
          'هل أنت متأكد من حذف المستخدم "${user.username}"؟\nهذا الإجراء لا يمكن التراجع عنه.',
      isDestructive: true,
    );

    if (confirmed == true) {
      final success = await ref
          .read(usersProvider.notifier)
          .deleteUser(user.id);

      if (success && mounted) {
        SnackBarHelper.showSuccess(
          context,
          'تم حذف المستخدم "${user.username}" بنجاح',
        );
      }
    }
  }

  Future<bool?> _showConfirmDialog({
    required String title,
    required String message,
    bool isDestructive = false,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              isDestructive ? Icons.warning : Icons.info,
              color: isDestructive ? Colors.red : Colors.blue,
            ),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: isDestructive ? Colors.red : Colors.blue,
            ),
            child: Text(isDestructive ? 'حذف' : 'تأكيد'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final usersState = ref.watch(usersProvider);
    final currentUser = authState.user;

    // Check if current user is admin
    if (currentUser?.role != UserRole.admin) {
      return Scaffold(
        appBar: AppBar(title: const Text('إدارة الصلاحيات')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.lock, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'ليس لديك صلاحية للوصول لهذه الصفحة',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    final filteredUsers = _getFilteredUsers(usersState.users);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الصلاحيات'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              ref.read(usersProvider.notifier).loadUsers();
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'البحث عن مستخدم...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged('');
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
            ),
          ),

          // Users List
          Expanded(
            child: usersState.isLoading
                ? const widgets.LoadingWidget(
                    message: 'جاري تحميل المستخدمين...',
                  )
                : usersState.error != null
                ? widgets.ErrorWidget(
                    message: usersState.error!,
                    onRetry: () {
                      ref.read(usersProvider.notifier).loadUsers();
                    },
                  )
                : filteredUsers.isEmpty
                ? widgets.EmptyStateWidget(
                    title: _searchQuery.isEmpty
                        ? 'لا يوجد مستخدمين'
                        : 'لا توجد نتائج',
                    subtitle: _searchQuery.isEmpty
                        ? 'لم يتم العثور على أي مستخدمين'
                        : 'جرب البحث بكلمات أخرى',
                    icon: Icons.people_outline,
                  )
                : RefreshIndicator(
                    onRefresh: () async {
                      await ref.read(usersProvider.notifier).loadUsers();
                    },
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: filteredUsers.length,
                      itemBuilder: (context, index) {
                        final user = filteredUsers[index];
                        final isCurrentUser = user.id == currentUser?.id;

                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: ListTile(
                            contentPadding: const EdgeInsets.all(16),
                            leading: CircleAvatar(
                              backgroundColor: user.role == UserRole.admin
                                  ? Colors.red[100]
                                  : Colors.blue[100],
                              child: Icon(
                                user.role == UserRole.admin
                                    ? FontAwesomeIcons.userShield
                                    : FontAwesomeIcons.user,
                                color: user.role == UserRole.admin
                                    ? Colors.red[700]
                                    : Colors.blue[700],
                                size: 20,
                              ),
                            ),
                            title: Row(
                              children: [
                                Text(
                                  user.username,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (isCurrentUser) ...[
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.green[100],
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      'أنت',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.green[700],
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 4),
                                Text(user.email),
                                const SizedBox(height: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: user.role == UserRole.admin
                                        ? Colors.red[50]
                                        : Colors.blue[50],
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: user.role == UserRole.admin
                                          ? Colors.red[200]!
                                          : Colors.blue[200]!,
                                    ),
                                  ),
                                  child: Text(
                                    user.role.displayName,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: user.role == UserRole.admin
                                          ? Colors.red[700]
                                          : Colors.blue[700],
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            trailing: isCurrentUser
                                ? null
                                : PopupMenuButton<String>(
                                    onSelected: (value) {
                                      switch (value) {
                                        case 'make_admin':
                                          _changeUserRole(user, UserRole.admin);
                                          break;
                                        case 'make_user':
                                          _changeUserRole(user, UserRole.user);
                                          break;
                                        case 'delete':
                                          _deleteUser(user);
                                          break;
                                      }
                                    },
                                    itemBuilder: (context) => [
                                      if (user.role != UserRole.admin)
                                        const PopupMenuItem(
                                          value: 'make_admin',
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.admin_panel_settings,
                                                size: 18,
                                              ),
                                              SizedBox(width: 8),
                                              Text('جعله مسؤول'),
                                            ],
                                          ),
                                        ),
                                      if (user.role != UserRole.user)
                                        const PopupMenuItem(
                                          value: 'make_user',
                                          child: Row(
                                            children: [
                                              Icon(Icons.person, size: 18),
                                              SizedBox(width: 8),
                                              Text('جعله مستخدم عادي'),
                                            ],
                                          ),
                                        ),
                                      const PopupMenuDivider(),
                                      const PopupMenuItem(
                                        value: 'delete',
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.delete,
                                              size: 18,
                                              color: Colors.red,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              'حذف المستخدم',
                                              style: TextStyle(
                                                color: Colors.red,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                        );
                      },
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
