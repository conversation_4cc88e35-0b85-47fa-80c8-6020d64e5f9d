# ===================================
# إعدادات Supabase
# ===================================

# رابط مشروع Supabase
SUPABASE_URL=https://your-project-id.supabase.co

# مفتاح API العام (anon key)
SUPABASE_ANON_KEY=your-anon-key-here

# مفتاح API الخدمة (service role key) - للاستخدام في الخادم فقط
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# ===================================
# إعدادات التطبيق
# ===================================

# اسم التطبيق
APP_NAME=نظام إدارة الانتخابات

# إصدار التطبيق
APP_VERSION=1.0.0

# بيئة التشغيل (development, staging, production)
ENVIRONMENT=development

# ===================================
# إعدادات المصادقة
# ===================================

# مدة انتهاء صلاحية الجلسة (بالثواني)
SESSION_TIMEOUT=3600

# تفعيل التحقق من البريد الإلكتروني
EMAIL_VERIFICATION_ENABLED=true

# ===================================
# إعدادات قاعدة البيانات
# ===================================

# حد أقصى لعدد النتائج في الصفحة الواحدة
MAX_PAGE_SIZE=50

# حد افتراضي لعدد النتائج في الصفحة
DEFAULT_PAGE_SIZE=20

# ===================================
# إعدادات الأمان
# ===================================

# تفعيل Row Level Security
RLS_ENABLED=true

# تفعيل تسجيل العمليات
AUDIT_LOGGING_ENABLED=true

# ===================================
# إعدادات التطوير
# ===================================

# تفعيل وضع التطوير
DEBUG_MODE=true

# عرض رسائل التطوير
SHOW_DEBUG_MESSAGES=true

# تفعيل Hot Reload
HOT_RELOAD_ENABLED=true
