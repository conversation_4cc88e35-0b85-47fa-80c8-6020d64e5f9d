# دليل إعداد Supabase للتطبيق

## الخطوة 1: إنشاء حساب Supabase

1. اذهب إلى [supabase.com](https://supabase.com)
2. اضغط على "Start your project"
3. قم بإنشاء حساب جديد أو تسجيل الدخول باستخدام GitHub

## الخطوة 2: إنشاء مشروع جديد

1. بعد تسجيل الدخول، اضغط على "New Project"
2. اختر Organization (أو أنشئ واحدة جديدة)
3. املأ البيانات التالية:
   - **Name**: intakhapat-app (أو أي اسم تفضله)
   - **Database Password**: كلمة مرور قوية (احفظها!)
   - **Region**: اختر أقرب منطقة لك
4. اضغط على "Create new project"
5. انتظر حتى يتم إنشاء المشروع (قد يستغرق دقيقتين)

## الخطوة 3: الحصول على مفاتيح API

1. في لوحة تحكم المشروع، اذهب إلى **Settings** > **API**
2. ستجد:
   - **Project URL**: انسخه
   - **anon public key**: انسخه
3. احفظ هذين المفتاحين، ستحتاجهما لاحقاً

## الخطوة 4: إنشاء الجداول

### 4.1 إنشاء جدول user_profiles

1. اذهب إلى **Table Editor** في الشريط الجانبي
2. اضغط على "Create a new table"
3. املأ البيانات:
   - **Name**: user_profiles
   - **Description**: جدول معلومات المستخدمين
4. اضغط "Save"

### 4.2 إضافة أعمدة جدول user_profiles

1. في جدول user_profiles، اضغط على "Add Column"
2. أضف الأعمدة التالية:

**العمود الأول: id**
- Name: id
- Type: uuid
- Default Value: (اتركه فارغ)
- Primary: ✅ مفعل
- Nullable: ❌ غير مفعل

**العمود الثاني: email**
- Name: email
- Type: text
- Default Value: (اتركه فارغ)
- Primary: ❌ غير مفعل
- Nullable: ❌ غير مفعل

**العمود الثالث: role**
- Name: role
- Type: text
- Default Value: 'user'
- Primary: ❌ غير مفعل
- Nullable: ❌ غير مفعل

**العمود الرابع: created_at**
- Name: created_at
- Type: timestamptz
- Default Value: now()
- Primary: ❌ غير مفعل
- Nullable: ✅ مفعل

### 4.3 إنشاء جدول voters

1. اضغط على "Create a new table" مرة أخرى
2. املأ البيانات:
   - **Name**: voters
   - **Description**: جدول بيانات الناخبين
3. اضغط "Save"

### 4.4 إضافة أعمدة جدول voters

**العمود الأول: id**
- Name: id
- Type: uuid
- Default Value: gen_random_uuid()
- Primary: ✅ مفعل
- Nullable: ❌ غير مفعل

**العمود الثاني: name**
- Name: name
- Type: text
- Default Value: (اتركه فارغ)
- Primary: ❌ غير مفعل
- Nullable: ❌ غير مفعل

**العمود الثالث: national_id**
- Name: national_id
- Type: text
- Default Value: (اتركه فارغ)
- Primary: ❌ غير مفعل
- Nullable: ❌ غير مفعل
- Unique: ✅ مفعل

**العمود الرابع: committee_location**
- Name: committee_location
- Type: text
- Default Value: (اتركه فارغ)
- Primary: ❌ غير مفعل
- Nullable: ❌ غير مفعل

**العمود الخامس: created_at**
- Name: created_at
- Type: timestamptz
- Default Value: now()
- Primary: ❌ غير مفعل
- Nullable: ✅ مفعل

**العمود السادس: updated_at**
- Name: updated_at
- Type: timestamptz
- Default Value: now()
- Primary: ❌ غير مفعل
- Nullable: ✅ مفعل

## الخطوة 5: إعداد المصادقة (Authentication)

1. اذهب إلى **Authentication** > **Settings**
2. في قسم **Site URL**، أضف:
   ```
   http://localhost:3000
   ```
3. في قسم **Redirect URLs**، أضف:
   ```
   http://localhost:3000/**
   ```
4. اضغط "Save"

## الخطوة 6: إعداد Row Level Security (RLS)

### 6.1 تفعيل RLS للجداول

1. اذهب إلى **Table Editor**
2. اختر جدول **user_profiles**
3. اضغط على أيقونة الترس ⚙️ بجانب اسم الجدول
4. اضغط على "Edit table"
5. فعّل "Enable Row Level Security (RLS)"
6. اضغط "Save"
7. كرر نفس الخطوات لجدول **voters**

### 6.2 إنشاء السياسات (Policies)

1. اذهب إلى **Authentication** > **Policies**

#### سياسات جدول user_profiles:

**سياسة القراءة:**
- Table: user_profiles
- Policy name: Users can view own profile
- Allowed operation: SELECT
- Target roles: authenticated
- USING expression:
  ```sql
  auth.uid() = id
  ```

**سياسة التحديث:**
- Table: user_profiles
- Policy name: Users can update own profile
- Allowed operation: UPDATE
- Target roles: authenticated
- USING expression:
  ```sql
  auth.uid() = id
  ```

#### سياسات جدول voters:

**سياسة القراءة:**
- Table: voters
- Policy name: Authenticated users can view voters
- Allowed operation: SELECT
- Target roles: authenticated
- USING expression:
  ```sql
  true
  ```

**سياسة الإدراج:**
- Table: voters
- Policy name: Authenticated users can insert voters
- Allowed operation: INSERT
- Target roles: authenticated
- WITH CHECK expression:
  ```sql
  true
  ```

**سياسة التحديث:**
- Table: voters
- Policy name: Authenticated users can update voters
- Allowed operation: UPDATE
- Target roles: authenticated
- USING expression:
  ```sql
  true
  ```

**سياسة الحذف:**
- Table: voters
- Policy name: Authenticated users can delete voters
- Allowed operation: DELETE
- Target roles: authenticated
- USING expression:
  ```sql
  true
  ```

## الخطوة 7: تحديث إعدادات التطبيق

1. افتح ملف `lib/config/supabase_config.dart`
2. استبدل القيم بالمفاتيح التي حصلت عليها:

```dart
class SupabaseConfig {
  static const String supabaseUrl = 'YOUR_PROJECT_URL_HERE';
  static const String supabaseAnonKey = 'YOUR_ANON_KEY_HERE';
}
```

## الخطوة 8: إنشاء مستخدم مسؤول

### 8.1 إنشاء المستخدم

1. اذهب إلى **Authentication** > **Users**
2. اضغط على "Add user"
3. املأ البيانات:
   - **Email**: <EMAIL> (أو أي إيميل تريده)
   - **Password**: كلمة مرور قوية
   - **Auto Confirm User**: ✅ مفعل
4. اضغط "Create user"

### 8.2 إضافة بيانات المسؤول

1. انسخ **User UID** من قائمة المستخدمين
2. اذهب إلى **Table Editor** > **user_profiles**
3. اضغط على "Insert" > "Insert row"
4. املأ البيانات:
   - **id**: الصق User UID هنا
   - **email**: <EMAIL>
   - **role**: admin
   - **created_at**: (سيتم ملؤه تلقائياً)
5. اضغط "Save"

## الخطوة 9: اختبار الإعداد

1. شغّل التطبيق:
   ```bash
   flutter run
   ```
2. جرب تسجيل الدخول بحساب المسؤول
3. جرب إضافة ناخب جديد
4. تأكد من أن جميع الوظائف تعمل

## استكشاف الأخطاء

### خطأ في الاتصال:
- تأكد من صحة URL و API Key
- تأكد من اتصال الإنترنت

### خطأ في المصادقة:
- تأكد من تفعيل RLS
- تأكد من إنشاء السياسات بشكل صحيح

### خطأ في إدراج البيانات:
- تأكد من أن جميع الحقول المطلوبة موجودة
- تأكد من عدم تكرار الرقم القومي

## نصائح مهمة

1. **احفظ كلمة مرور قاعدة البيانات** في مكان آمن
2. **لا تشارك مفاتيح API** مع أحد
3. **استخدم متغيرات البيئة** في الإنتاج
4. **فعّل النسخ الاحتياطي** في الإعدادات

---

بعد إتمام هذه الخطوات، سيكون تطبيقك جاهزاً للاستخدام مع قاعدة بيانات Supabase مكتملة!
