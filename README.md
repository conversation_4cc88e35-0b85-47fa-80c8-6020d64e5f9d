# نظام إدارة الانتخابات - Intakhapat

تطبيق Flutter لإدارة بيانات الناخبين باستخدام MVVM pattern مع Supabase كقاعدة بيانات.

## المميزات

### 🔐 نظام المصادقة
- تسجيل دخول آمن باستخدام البريد الإلكتروني وكلمة المرور
- نظام صلاحيات (مسؤول / مستخدم عادي)
- إدارة جلسات المستخدمين

### 👥 إدارة الناخبين
- إضافة بيانات الناخبين (الاسم، الرقم القومي، مكان اللجنة)
- تعديل البيانات الموجودة
- حذف البيانات (للمسؤولين فقط)
- البحث في البيانات بالاسم أو الرقم القومي أو مكان اللجنة

### 🔍 البحث والتصفية
- بحث فوري في البيانات
- تصفح البيانات مع Pagination
- عرض إحصائيات سريعة

### 📱 تصميم متجاوب
- يعمل على جميع أحجام الشاشات
- تصميم Material Design 3
- واجهة مستخدم باللغة العربية

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **Riverpod**: إدارة الحالة
- **Supabase**: قاعدة البيانات والمصادقة
- **Go Router**: التنقل بين الشاشات
- **MVVM Pattern**: نمط التصميم المعماري

## متطلبات التشغيل

- Flutter SDK 3.8.1 أو أحدث
- Dart SDK 3.0.0 أو أحدث
- حساب Supabase

## الإعداد والتثبيت

### 1. إعداد Supabase

1. إنشاء مشروع جديد في [Supabase](https://supabase.com)
2. إنشاء الجداول التالية:

#### جدول user_profiles
```sql
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### جدول voters
```sql
CREATE TABLE voters (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  national_id TEXT UNIQUE NOT NULL,
  committee_location TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. إعداد التطبيق

1. استنساخ المشروع
2. تثبيت المكتبات: `flutter pub get`
3. تحديث إعدادات Supabase في `lib/config/supabase_config.dart`
4. تشغيل التطبيق: `flutter run`

## الاستخدام

### تسجيل الدخول
- أدخل البريد الإلكتروني وكلمة المرور

### إضافة ناخب جديد
- اضغط على زر "+" واملأ البيانات المطلوبة

### البحث
- استخدم شريط البحث للبحث بالاسم أو الرقم القومي

## نظام الصلاحيات

- **المسؤول**: جميع العمليات (عرض، إضافة، تعديل، حذف)
- **المستخدم العادي**: عرض البيانات والبحث فقط
