class VoterModel {
  final String? id;
  final String name;
  final String nationalId;
  final String committeeLocation;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  VoterModel({
    this.id,
    required this.name,
    required this.nationalId,
    required this.committeeLocation,
    this.createdAt,
    this.updatedAt,
  });

  factory VoterModel.fromJson(Map<String, dynamic> json) {
    return VoterModel(
      id: json['id']?.toString(),
      name: json['name'] ?? '',
      nationalId: json['national_id'] ?? '',
      committeeLocation: json['committee_location'] ?? '',
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'national_id': nationalId,
      'committee_location': committeeLocation,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  VoterModel copyWith({
    String? id,
    String? name,
    String? nationalId,
    String? committeeLocation,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VoterModel(
      id: id ?? this.id,
      name: name ?? this.name,
      nationalId: nationalId ?? this.nationalId,
      committeeLocation: committeeLocation ?? this.committeeLocation,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VoterModel &&
        other.id == id &&
        other.name == name &&
        other.nationalId == nationalId &&
        other.committeeLocation == committeeLocation;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        nationalId.hashCode ^
        committeeLocation.hashCode;
  }

  @override
  String toString() {
    return 'VoterModel(id: $id, name: $name, nationalId: $nationalId, committeeLocation: $committeeLocation)';
  }
}
