import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/user_model.dart';
import '../models/voter_model.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  SupabaseClient get client => Supabase.instance.client;

  static Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
    );
  }

  // Authentication Methods
  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<AuthResponse> signInWithPhone({
    required String phone,
    required String password,
  }) async {
    try {
      // For now, we'll use email-based authentication
      // In a real implementation, you might want to use phone authentication
      // This is a simplified approach where phone is treated as username
      final response = await client.auth.signInWithPassword(
        email: phone, // This would need to be adapted for actual phone auth
        password: password,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await client.auth.signUp(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> signOut() async {
    await client.auth.signOut();
  }

  User? get currentUser => client.auth.currentUser;

  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;

  // User Profile Methods
  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final response = await client
          .from('user_profiles')
          .select()
          .eq('id', userId)
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  Future<UserModel> createUserProfile({
    required String userId,
    required String email,
    String? phone,
    required UserRole role,
  }) async {
    final userData = {
      'id': userId,
      'email': email,
      if (phone != null) 'phone': phone,
      'role': role.toString().split('.').last,
      'created_at': DateTime.now().toIso8601String(),
    };

    final response = await client
        .from('user_profiles')
        .insert(userData)
        .select()
        .single();

    return UserModel.fromJson(response);
  }

  // Voters CRUD Methods
  Future<List<VoterModel>> getVoters({
    String? searchQuery,
    int? limit,
    int? offset,
  }) async {
    try {
      // Build the query step by step
      var queryBuilder = client.from('voters').select();

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryBuilder = queryBuilder.or(
          'name.ilike.%$searchQuery%,'
          'national_id.ilike.%$searchQuery%,'
          'committee_location.ilike.%$searchQuery%',
        );
      }

      // Apply ordering, limit, and range
      var finalQuery = queryBuilder.order('created_at', ascending: false);

      if (offset != null && limit != null) {
        finalQuery = finalQuery.range(offset, offset + limit - 1);
      } else if (limit != null) {
        finalQuery = finalQuery.limit(limit);
      }

      final response = await finalQuery;

      return (response as List)
          .map((json) => VoterModel.fromJson(json))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  Future<VoterModel> createVoter(VoterModel voter) async {
    try {
      // Check if user is admin
      if (currentUser != null) {
        final userProfile = await getUserProfile(currentUser!.id);
        if (userProfile?.role != UserRole.admin) {
          throw Exception('غير مصرح لك بإضافة مسجلين');
        }
      }

      final voterData = voter.toJson();
      voterData['created_at'] = DateTime.now().toIso8601String();
      voterData['updated_at'] = DateTime.now().toIso8601String();

      final response = await client
          .from('voters')
          .insert(voterData)
          .select()
          .single();

      return VoterModel.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  Future<VoterModel> updateVoter(VoterModel voter) async {
    try {
      // Check if user is admin
      if (currentUser != null) {
        final userProfile = await getUserProfile(currentUser!.id);
        if (userProfile?.role != UserRole.admin) {
          throw Exception('غير مصرح لك بتعديل المسجلين');
        }
      }

      final voterData = voter.toJson();
      voterData['updated_at'] = DateTime.now().toIso8601String();

      final response = await client
          .from('voters')
          .update(voterData)
          .eq('id', voter.id!)
          .select()
          .single();

      return VoterModel.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> deleteVoter(String voterId) async {
    try {
      // Check if user is admin
      if (currentUser != null) {
        final userProfile = await getUserProfile(currentUser!.id);
        if (userProfile?.role != UserRole.admin) {
          throw Exception('غير مصرح لك بحذف المسجلين');
        }
      }

      await client.from('voters').delete().eq('id', voterId);
    } catch (e) {
      rethrow;
    }
  }

  Future<int> getVotersCount({String? searchQuery}) async {
    try {
      var query = client.from('voters').select('id');

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or(
          'name.ilike.%$searchQuery%,'
          'national_id.ilike.%$searchQuery%,'
          'committee_location.ilike.%$searchQuery%',
        );
      }

      final response = await query;
      return (response as List).length;
    } catch (e) {
      return 0;
    }
  }

  // Check if national ID already exists
  Future<bool> isNationalIdExists(
    String nationalId, {
    String? excludeId,
  }) async {
    try {
      var query = client
          .from('voters')
          .select('id')
          .eq('national_id', nationalId);

      if (excludeId != null) {
        query = query.neq('id', excludeId);
      }

      final response = await query;
      return response.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Committee Locations Methods
  Future<List<String>> getCommitteeLocations({String? searchQuery}) async {
    try {
      var query = client
          .from('committee_locations')
          .select('name')
          .eq('is_active', true)
          .order('name');

      // For now, we'll get all locations and filter client-side
      // In production, you might want to implement server-side filtering
      final response = await query.limit(50);
      final allLocations = (response as List)
          .map((item) => item['name'] as String)
          .toList();

      if (searchQuery != null && searchQuery.isNotEmpty) {
        return allLocations
            .where(
              (location) =>
                  location.toLowerCase().contains(searchQuery.toLowerCase()),
            )
            .toList();
      }

      return allLocations;
    } catch (e) {
      return [];
    }
  }

  Future<void> addCommitteeLocation(
    String name, {
    String? governorate,
    String? district,
  }) async {
    try {
      await client.from('committee_locations').insert({
        'name': name,
        'governorate': governorate,
        'district': district,
        'is_active': true,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Enhanced user profile methods
  Future<UserModel> updateUserProfile({
    required String userId,
    String? email,
    String? phone,
    UserRole? role,
  }) async {
    final updateData = <String, dynamic>{};

    if (email != null) updateData['email'] = email;
    if (phone != null) updateData['phone'] = phone;
    if (role != null) updateData['role'] = role.toString().split('.').last;

    updateData['updated_at'] = DateTime.now().toIso8601String();

    final response = await client
        .from('user_profiles')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single();

    return UserModel.fromJson(response);
  }

  // Users management methods (Admin only)
  Future<List<UserModel>> getUsers({
    String? searchQuery,
    int? limit,
    int? offset,
  }) async {
    try {
      // Check if current user is admin
      if (currentUser != null) {
        final userProfile = await getUserProfile(currentUser!.id);
        if (userProfile?.role != UserRole.admin) {
          throw Exception('غير مصرح لك بعرض قائمة المستخدمين');
        }
      }

      var queryBuilder = client.from('user_profiles').select();

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryBuilder = queryBuilder.or(
          'email.ilike.%$searchQuery%,'
          'phone.ilike.%$searchQuery%',
        );
      }

      var finalQuery = queryBuilder.order('created_at', ascending: false);

      if (limit != null) {
        finalQuery = finalQuery.limit(limit);
      }

      if (offset != null) {
        finalQuery = finalQuery.range(offset, offset + (limit ?? 20) - 1);
      }

      final response = await finalQuery;

      return (response as List)
          .map((json) => UserModel.fromJson(json))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  Future<int> getUsersCount({String? searchQuery}) async {
    try {
      // Check if current user is admin
      if (currentUser != null) {
        final userProfile = await getUserProfile(currentUser!.id);
        if (userProfile?.role != UserRole.admin) {
          throw Exception('غير مصرح لك بعرض إحصائيات المستخدمين');
        }
      }

      var query = client.from('user_profiles').select('id');

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or(
          'email.ilike.%$searchQuery%,'
          'phone.ilike.%$searchQuery%',
        );
      }

      final response = await query;
      return (response as List).length;
    } catch (e) {
      rethrow;
    }
  }

  Future<UserModel> createUserByAdmin({
    required String email,
    required String password,
    String? phone,
    required UserRole role,
  }) async {
    try {
      // Check if current user is admin
      if (currentUser != null) {
        final userProfile = await getUserProfile(currentUser!.id);
        if (userProfile?.role != UserRole.admin) {
          throw Exception('غير مصرح لك بإنشاء مستخدمين جدد');
        }
      }

      // First check if email already exists
      try {
        final existingUsers = await client
            .from('user_profiles')
            .select('email')
            .eq('email', email)
            .limit(1);

        if (existingUsers.isNotEmpty) {
          throw Exception('البريد الإلكتروني مستخدم بالفعل');
        }
      } catch (e) {
        if (e.toString().contains('البريد الإلكتروني مستخدم بالفعل')) {
          rethrow;
        }
        // Continue if it's just a query error
      }

      // Try to create auth user with admin service role first
      User? newUser;
      try {
        final userResponse = await client.auth.admin.createUser(
          AdminUserAttributes(
            email: email,
            password: password,
            emailConfirm: true, // Auto-confirm email for admin-created users
          ),
        );
        newUser = userResponse.user;
      } catch (adminError) {
        // Fallback to regular signup if admin API is not available
        final authResponse = await client.auth.signUp(
          email: email,
          password: password,
        );
        newUser = authResponse.user;
      }

      if (newUser == null) {
        throw Exception('فشل في إنشاء المستخدم في نظام المصادقة');
      }

      // Create user profile
      final userProfile = await createUserProfile(
        userId: newUser.id,
        email: email,
        phone: phone,
        role: role,
      );

      return userProfile;
    } catch (e) {
      // Re-throw with more context if needed
      if (e is Exception) {
        rethrow;
      } else {
        throw Exception('خطأ في إنشاء المستخدم: ${e.toString()}');
      }
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      // Check if current user is admin
      if (currentUser != null) {
        final userProfile = await getUserProfile(currentUser!.id);
        if (userProfile?.role != UserRole.admin) {
          throw Exception('غير مصرح لك بحذف المستخدمين');
        }
      }

      // Don't allow deleting self
      if (currentUser?.id == userId) {
        throw Exception('لا يمكنك حذف حسابك الخاص');
      }

      // Delete user profile (auth user will be handled by RLS)
      await client.from('user_profiles').delete().eq('id', userId);
    } catch (e) {
      rethrow;
    }
  }

  // Get all users (admin only)
  Future<List<UserModel>> getAllUsers() async {
    try {
      final response = await client
          .from('user_profiles')
          .select()
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => UserModel.fromJson(json))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  // Statistics methods
  Future<Map<String, int>> getStatistics() async {
    try {
      // Get total voters count
      final votersResponse = await client.from('voters').select('id');
      final votersCount = (votersResponse as List).length;

      // Get total users count
      final usersResponse = await client.from('user_profiles').select('id');
      final usersCount = (usersResponse as List).length;

      // Get admin users count
      final adminsResponse = await client
          .from('user_profiles')
          .select('id')
          .eq('role', 'admin');
      final adminsCount = (adminsResponse as List).length;

      // Get committee locations count
      final locationsResponse = await client
          .from('committee_locations')
          .select('id')
          .eq('is_active', true);
      final locationsCount = (locationsResponse as List).length;

      return {
        'total_voters': votersCount,
        'total_users': usersCount,
        'total_admins': adminsCount,
        'total_locations': locationsCount,
      };
    } catch (e) {
      return {
        'total_voters': 0,
        'total_users': 0,
        'total_admins': 0,
        'total_locations': 0,
      };
    }
  }

  // Bulk operations
  Future<List<VoterModel>> bulkCreateVoters(List<VoterModel> voters) async {
    try {
      final votersData = voters.map((voter) {
        final data = voter.toJson();
        data['created_at'] = DateTime.now().toIso8601String();
        data['updated_at'] = DateTime.now().toIso8601String();
        data['created_by'] = currentUser?.id;
        return data;
      }).toList();

      final response = await client.from('voters').insert(votersData).select();

      return (response as List)
          .map((json) => VoterModel.fromJson(json))
          .toList();
    } catch (e) {
      rethrow;
    }
  }
}
