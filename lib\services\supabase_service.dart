import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/user_model.dart';
import '../models/voter_model.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  SupabaseClient get client => Supabase.instance.client;

  static Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
    );
  }

  // Authentication Methods
  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<AuthResponse> signInWithUsername({
    required String username,
    required String password,
  }) async {
    try {
      // First, get the email associated with the username
      final userProfile = await client
          .from('user_profiles')
          .select('email')
          .eq('username', username)
          .single();

      final email = userProfile['email'] as String;

      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> signOut() async {
    await client.auth.signOut();
  }

  User? get currentUser => client.auth.currentUser;

  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;

  // User Profile Methods
  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final response = await client
          .from('user_profiles')
          .select()
          .eq('id', userId)
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  Future<UserModel> createUserProfile({
    required String userId,
    required String username,
    required String email,
    required UserRole role,
  }) async {
    final userData = {
      'id': userId,
      'username': username,
      'email': email,
      'role': role.toString().split('.').last,
      'created_at': DateTime.now().toIso8601String(),
    };

    final response = await client
        .from('user_profiles')
        .insert(userData)
        .select()
        .single();

    return UserModel.fromJson(response);
  }

  // Users Management Methods
  Future<List<UserModel>> getAllUsers() async {
    try {
      final response = await client
          .from('user_profiles')
          .select()
          .order('created_at', ascending: false);

      return (response as List)
          .map((user) => UserModel.fromJson(user))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateUserRole(String userId, UserRole newRole) async {
    try {
      await client
          .from('user_profiles')
          .update({'role': newRole.toString().split('.').last})
          .eq('id', userId);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      // Delete from user_profiles table
      await client.from('user_profiles').delete().eq('id', userId);

      // Note: Deleting from auth.users requires admin privileges
      // This would typically be done through Supabase Admin API
      // For now, we only delete from user_profiles
    } catch (e) {
      rethrow;
    }
  }

  // Voters CRUD Methods
  Future<List<VoterModel>> getVoters({
    String? searchQuery,
    int? limit,
    int? offset,
  }) async {
    try {
      var queryBuilder = client.from('voters').select();

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryBuilder = queryBuilder.or(
          'name.ilike.%$searchQuery%,'
          'national_id.ilike.%$searchQuery%,'
          'committee_location.ilike.%$searchQuery%',
        );
      }

      var finalQuery = queryBuilder.order('created_at', ascending: false);

      if (limit != null) {
        finalQuery = finalQuery.limit(limit);
      }

      if (offset != null) {
        finalQuery = finalQuery.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await finalQuery;

      return (response as List)
          .map((json) => VoterModel.fromJson(json))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  Future<VoterModel> createVoter(VoterModel voter) async {
    try {
      final voterData = voter.toJson();
      voterData['created_at'] = DateTime.now().toIso8601String();
      voterData['updated_at'] = DateTime.now().toIso8601String();

      final response = await client
          .from('voters')
          .insert(voterData)
          .select()
          .single();

      return VoterModel.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  Future<VoterModel> updateVoter(VoterModel voter) async {
    try {
      final voterData = voter.toJson();
      voterData['updated_at'] = DateTime.now().toIso8601String();

      final response = await client
          .from('voters')
          .update(voterData)
          .eq('id', voter.id!)
          .select()
          .single();

      return VoterModel.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> deleteVoter(String voterId) async {
    try {
      await client.from('voters').delete().eq('id', voterId);
    } catch (e) {
      rethrow;
    }
  }

  Future<int> getVotersCount({String? searchQuery}) async {
    try {
      var query = client.from('voters').select('id');

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or(
          'name.ilike.%$searchQuery%,'
          'national_id.ilike.%$searchQuery%,'
          'committee_location.ilike.%$searchQuery%',
        );
      }

      final response = await query;
      return (response as List).length;
    } catch (e) {
      return 0;
    }
  }

  // Check if national ID already exists
  Future<bool> isNationalIdExists(
    String nationalId, {
    String? excludeId,
  }) async {
    try {
      var query = client
          .from('voters')
          .select('id')
          .eq('national_id', nationalId);

      if (excludeId != null) {
        query = query.neq('id', excludeId);
      }

      final response = await query;
      return response.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
