import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../models/voter_model.dart';

class ExportService {
  static const String _fileName = 'voters_export';

  /// Export voters data to CSV format
  static Future<String> exportToCSV(List<VoterModel> voters) async {
    final buffer = StringBuffer();
    
    // Add CSV header
    buffer.writeln('الاسم,الرقم القومي,مكان اللجنة,تاريخ الإنشاء');
    
    // Add data rows
    for (final voter in voters) {
      final row = [
        _escapeCsvField(voter.name),
        _escapeCsvField(voter.nationalId),
        _escapeCsvField(voter.committeeLocation),
        voter.createdAt?.toString().split(' ')[0] ?? '',
      ].join(',');
      buffer.writeln(row);
    }
    
    return buffer.toString();
  }

  /// Export voters data to JSON format
  static Future<String> exportToJSON(List<VoterModel> voters) async {
    final data = {
      'export_date': DateTime.now().toIso8601String(),
      'total_count': voters.length,
      'voters': voters.map((voter) => voter.toJson()).toList(),
    };
    
    const encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(data);
  }

  /// Save export data to file and share
  static Future<void> shareExport({
    required List<VoterModel> voters,
    required ExportFormat format,
  }) async {
    try {
      String content;
      String extension;
      String mimeType;

      switch (format) {
        case ExportFormat.csv:
          content = await exportToCSV(voters);
          extension = 'csv';
          mimeType = 'text/csv';
          break;
        case ExportFormat.json:
          content = await exportToJSON(voters);
          extension = 'json';
          mimeType = 'application/json';
          break;
      }

      if (kIsWeb) {
        // For web, use the share API or download
        await _shareOnWeb(content, extension, mimeType);
      } else {
        // For mobile/desktop, save to file and share
        await _shareOnMobile(content, extension, mimeType);
      }
    } catch (e) {
      throw Exception('فشل في تصدير البيانات: ${e.toString()}');
    }
  }

  /// Share on web platform
  static Future<void> _shareOnWeb(String content, String extension, String mimeType) async {
    // For web, we can use the share API or trigger a download
    // This is a simplified implementation
    if (await Share.canShare()) {
      await Share.share(
        content,
        subject: 'تصدير بيانات المسجلين - ${DateTime.now().toString().split(' ')[0]}',
      );
    } else {
      // Fallback: copy to clipboard or show content
      throw Exception('المشاركة غير متاحة على هذا المتصفح');
    }
  }

  /// Share on mobile/desktop platform
  static Future<void> _shareOnMobile(String content, String extension, String mimeType) async {
    final directory = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = '${_fileName}_$timestamp.$extension';
    final file = File('${directory.path}/$fileName');
    
    await file.writeAsString(content, encoding: utf8);
    
    await Share.shareXFiles(
      [XFile(file.path, mimeType: mimeType)],
      subject: 'تصدير بيانات المسجلين - ${DateTime.now().toString().split(' ')[0]}',
      text: 'ملف تصدير بيانات المسجلين من نظام إدارة الانتخابات',
    );
  }

  /// Generate export summary
  static Map<String, dynamic> generateSummary(List<VoterModel> voters) {
    final summary = <String, dynamic>{
      'total_voters': voters.length,
      'export_date': DateTime.now().toIso8601String(),
      'committees': <String, int>{},
    };

    // Count voters by committee
    for (final voter in voters) {
      final committee = voter.committeeLocation;
      summary['committees'][committee] = (summary['committees'][committee] ?? 0) + 1;
    }

    // Sort committees by count
    final sortedCommittees = Map.fromEntries(
      (summary['committees'] as Map<String, int>).entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value)),
    );
    summary['committees'] = sortedCommittees;

    return summary;
  }

  /// Escape CSV field to handle commas and quotes
  static String _escapeCsvField(String field) {
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
      return '"${field.replaceAll('"', '""')}"';
    }
    return field;
  }

  /// Validate export data
  static bool validateExportData(List<VoterModel> voters) {
    if (voters.isEmpty) return false;
    
    // Check for required fields
    for (final voter in voters) {
      if (voter.name.trim().isEmpty || 
          voter.nationalId.trim().isEmpty || 
          voter.committeeLocation.trim().isEmpty) {
        return false;
      }
    }
    
    return true;
  }

  /// Get export file size estimate
  static int getEstimatedFileSize(List<VoterModel> voters, ExportFormat format) {
    switch (format) {
      case ExportFormat.csv:
        // Rough estimate: average 100 characters per row
        return voters.length * 100;
      case ExportFormat.json:
        // Rough estimate: average 200 characters per voter in JSON
        return voters.length * 200 + 500; // +500 for metadata
    }
  }

  /// Check if export is supported on current platform
  static bool isExportSupported() {
    return true; // Both mobile and web support some form of export
  }

  /// Get available export formats
  static List<ExportFormat> getAvailableFormats() {
    return ExportFormat.values;
  }
}

enum ExportFormat {
  csv('CSV', 'ملف CSV (Excel)', 'text/csv'),
  json('JSON', 'ملف JSON', 'application/json');

  const ExportFormat(this.code, this.displayName, this.mimeType);

  final String code;
  final String displayName;
  final String mimeType;
}

/// Export options class
class ExportOptions {
  final ExportFormat format;
  final bool includeMetadata;
  final bool includeTimestamp;
  final String? customFileName;

  const ExportOptions({
    required this.format,
    this.includeMetadata = true,
    this.includeTimestamp = true,
    this.customFileName,
  });

  ExportOptions copyWith({
    ExportFormat? format,
    bool? includeMetadata,
    bool? includeTimestamp,
    String? customFileName,
  }) {
    return ExportOptions(
      format: format ?? this.format,
      includeMetadata: includeMetadata ?? this.includeMetadata,
      includeTimestamp: includeTimestamp ?? this.includeTimestamp,
      customFileName: customFileName ?? this.customFileName,
    );
  }
}

/// Export result class
class ExportResult {
  final bool success;
  final String? filePath;
  final String? error;
  final int recordCount;
  final ExportFormat format;

  const ExportResult({
    required this.success,
    this.filePath,
    this.error,
    required this.recordCount,
    required this.format,
  });

  factory ExportResult.success({
    required String filePath,
    required int recordCount,
    required ExportFormat format,
  }) {
    return ExportResult(
      success: true,
      filePath: filePath,
      recordCount: recordCount,
      format: format,
    );
  }

  factory ExportResult.failure({
    required String error,
    required ExportFormat format,
  }) {
    return ExportResult(
      success: false,
      error: error,
      recordCount: 0,
      format: format,
    );
  }
}
