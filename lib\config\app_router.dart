import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../providers/auth_provider.dart';
import '../screens/login_screen.dart';
import '../screens/home_screen.dart';
import '../screens/voter_form_screen.dart';
import '../screens/permissions_management_screen.dart';

// Route names
class AppRoutes {
  static const String login = '/login';
  static const String home = '/';
  static const String addVoter = '/add-voter';
  static const String editVoter = '/edit-voter';
  static const String permissions = '/permissions';
}

// Router provider
final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);

  return GoRouter(
    initialLocation: authState.isAuthenticated
        ? AppRoutes.home
        : AppRoutes.login,
    redirect: (context, state) {
      final isAuthenticated = authState.isAuthenticated;
      final isLoggingIn = state.matchedLocation == AppRoutes.login;

      // If not authenticated and not on login page, redirect to login
      if (!isAuthenticated && !isLoggingIn) {
        return AppRoutes.login;
      }

      // If authenticated and on login page, redirect to home
      if (isAuthenticated && isLoggingIn) {
        return AppRoutes.home;
      }

      // No redirect needed
      return null;
    },
    routes: [
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: AppRoutes.addVoter,
        name: 'add-voter',
        builder: (context, state) => const VoterFormScreen(),
      ),
      GoRoute(
        path: '${AppRoutes.editVoter}/:id',
        name: 'edit-voter',
        builder: (context, state) {
          final voterId = state.pathParameters['id']!;
          return VoterFormScreen(voterId: voterId);
        },
      ),
      GoRoute(
        path: AppRoutes.permissions,
        name: 'permissions',
        builder: (context, state) => const PermissionsManagementScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('خطأ')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'الصفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: ${state.matchedLocation}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation helpers
extension AppNavigation on BuildContext {
  void goToLogin() => go(AppRoutes.login);
  void goToHome() => go(AppRoutes.home);
  void goToAddVoter() => go(AppRoutes.addVoter);
  void goToEditVoter(String voterId) => go('${AppRoutes.editVoter}/$voterId');
  void goToPermissions() => go(AppRoutes.permissions);

  void pushAddVoter() => push(AppRoutes.addVoter);
  void pushEditVoter(String voterId) => push('${AppRoutes.editVoter}/$voterId');
  void pushPermissions() => push(AppRoutes.permissions);
}
