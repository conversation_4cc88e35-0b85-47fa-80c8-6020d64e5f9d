class Validators {
  // Email validation
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    
    return null;
  }

  // Password validation
  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    
    if (value.length < 6) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    return null;
  }

  // Name validation
  static String? name(String? value) {
    if (value == null || value.isEmpty) {
      return 'الاسم مطلوب';
    }
    
    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }
    
    // Check if name contains only Arabic letters, English letters, and spaces
    final nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$');
    if (!nameRegex.hasMatch(value.trim())) {
      return 'الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط';
    }
    
    return null;
  }

  // National ID validation (Egyptian format)
  static String? nationalId(String? value) {
    if (value == null || value.isEmpty) {
      return 'الرقم القومي مطلوب';
    }
    
    // Remove any spaces or dashes
    final cleanValue = value.replaceAll(RegExp(r'[\s-]'), '');
    
    // Check if it's exactly 14 digits
    if (cleanValue.length != 14) {
      return 'الرقم القومي يجب أن يكون 14 رقم';
    }
    
    // Check if all characters are digits
    if (!RegExp(r'^\d+$').hasMatch(cleanValue)) {
      return 'الرقم القومي يجب أن يحتوي على أرقام فقط';
    }
    
    // Basic validation for Egyptian national ID format
    // First digit should be 2 or 3 (for birth years 1900-2099)
    final firstDigit = int.parse(cleanValue[0]);
    if (firstDigit < 2 || firstDigit > 3) {
      return 'الرقم القومي غير صحيح';
    }
    
    // Check birth year (first two digits)
    final birthYear = int.parse(cleanValue.substring(0, 2));
    final currentYear = DateTime.now().year % 100;
    
    // Validate birth year range
    if (firstDigit == 2 && birthYear > currentYear + 10) {
      return 'الرقم القومي غير صحيح';
    }
    
    // Check month (digits 3-4)
    final birthMonth = int.parse(cleanValue.substring(2, 4));
    if (birthMonth < 1 || birthMonth > 12) {
      return 'الرقم القومي غير صحيح - الشهر غير صحيح';
    }
    
    // Check day (digits 5-6)
    final birthDay = int.parse(cleanValue.substring(4, 6));
    if (birthDay < 1 || birthDay > 31) {
      return 'الرقم القومي غير صحيح - اليوم غير صحيح';
    }
    
    return null;
  }

  // Committee location validation
  static String? committeeLocation(String? value) {
    if (value == null || value.isEmpty) {
      return 'مكان اللجنة مطلوب';
    }
    
    if (value.trim().length < 3) {
      return 'مكان اللجنة يجب أن يكون 3 أحرف على الأقل';
    }
    
    return null;
  }

  // Phone number validation (Egyptian format)
  static String? phoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone is optional
    }
    
    // Remove any spaces, dashes, or plus signs
    final cleanValue = value.replaceAll(RegExp(r'[\s\-\+]'), '');
    
    // Egyptian phone number patterns
    final egyptianMobileRegex = RegExp(r'^(010|011|012|015)\d{8}$');
    final egyptianLandlineRegex = RegExp(r'^0[2-9]\d{7,8}$');
    final internationalEgyptianRegex = RegExp(r'^20(10|11|12|15)\d{8}$');
    
    if (!egyptianMobileRegex.hasMatch(cleanValue) && 
        !egyptianLandlineRegex.hasMatch(cleanValue) && 
        !internationalEgyptianRegex.hasMatch(cleanValue)) {
      return 'رقم الهاتف غير صحيح';
    }
    
    return null;
  }

  // Generic required field validation
  static String? required(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'هذا الحقل'} مطلوب';
    }
    return null;
  }

  // Minimum length validation
  static String? Function(String?) minLength(int min, [String? fieldName]) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle empty values
      }
      
      if (value.length < min) {
        return '${fieldName ?? 'هذا الحقل'} يجب أن يكون $min أحرف على الأقل';
      }
      
      return null;
    };
  }

  // Maximum length validation
  static String? Function(String?) maxLength(int max, [String? fieldName]) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle empty values
      }
      
      if (value.length > max) {
        return '${fieldName ?? 'هذا الحقل'} يجب أن يكون $max أحرف كحد أقصى';
      }
      
      return null;
    };
  }

  // Combine multiple validators
  static String? Function(String?) combine(List<String? Function(String?)> validators) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }
      return null;
    };
  }
}
