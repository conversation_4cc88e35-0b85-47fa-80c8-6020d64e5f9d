# إعداد قاعدة البيانات - Supabase

## نظرة عامة

هذا الدليل يوضح كيفية إعداد قاعدة البيانات في Supabase لتطبيق إدارة الانتخابات.

## الخطوات المطلوبة

### 1. إنشاء مشروع Supabase جديد

1. اذهب إلى [Supabase Dashboard](https://app.supabase.com)
2. قم بإنشاء مشروع جديد
3. احفظ URL المشروع و API Key

### 2. تشغيل SQL Script

1. اذهب إلى SQL Editor في لوحة تحكم Supabase
2. انسخ محتوى ملف `supabase_setup.sql`
3. قم بتشغيل الـ script

### 3. تحديث إعدادات التطبيق

قم بتحديث ملف `lib/config/supabase_config.dart`:

```dart
class SupabaseConfig {
  static const String supabaseUrl = 'YOUR_SUPABASE_PROJECT_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
}
```

### 4. إنشاء مستخدم مسؤول

1. قم بتسجيل مستخدم جديد في التطبيق
2. احصل على User ID من جدول `auth.users`
3. قم بتحديث دور المستخدم إلى admin:

```sql
UPDATE public.user_profiles 
SET role = 'admin' 
WHERE id = 'USER_ID_HERE';
```

## هيكل قاعدة البيانات

### الجداول الرئيسية

#### 1. user_profiles
- `id`: UUID (مرجع إلى auth.users)
- `email`: البريد الإلكتروني
- `phone`: رقم الهاتف (اختياري)
- `role`: الدور (admin/user)
- `created_at`: تاريخ الإنشاء
- `updated_at`: تاريخ آخر تحديث

#### 2. voters
- `id`: UUID (المفتاح الأساسي)
- `name`: اسم المسجل
- `national_id`: الرقم القومي (فريد)
- `committee_location`: مكان اللجنة
- `created_at`: تاريخ الإنشاء
- `updated_at`: تاريخ آخر تحديث
- `created_by`: مرجع إلى المستخدم الذي أضاف البيانات

#### 3. committee_locations
- `id`: UUID (المفتاح الأساسي)
- `name`: اسم اللجنة
- `governorate`: المحافظة
- `district`: المنطقة
- `is_active`: حالة النشاط
- `created_at`: تاريخ الإنشاء

### الصلاحيات (RLS Policies)

#### للمستخدمين العاديين (User):
- عرض جميع بيانات المسجلين
- عرض أماكن اللجان
- عرض وتعديل ملفهم الشخصي فقط

#### للمسؤولين (Admin):
- جميع صلاحيات المستخدم العادي
- إضافة وتعديل وحذف بيانات المسجلين
- إدارة أماكن اللجان
- عرض جميع ملفات المستخدمين

### الفهارس (Indexes)

تم إنشاء فهارس لتحسين الأداء على:
- الرقم القومي
- اسم المسجل
- مكان اللجنة
- البريد الإلكتروني
- دور المستخدم

### المشغلات (Triggers)

- تحديث تلقائي لـ `updated_at` عند تعديل البيانات
- إنشاء ملف شخصي تلقائياً عند تسجيل مستخدم جديد

## الأمان

- تم تفعيل Row Level Security على جميع الجداول
- المستخدمون يمكنهم الوصول فقط للبيانات المسموح لهم بها
- الأرقام القومية فريدة لمنع التكرار
- جميع العمليات الحساسة تتطلب صلاحيات المسؤول

## اختبار الإعداد

بعد تشغيل الـ script، يمكنك اختبار الإعداد من خلال:

1. تسجيل مستخدم جديد في التطبيق
2. التحقق من إنشاء ملف شخصي تلقائياً
3. ترقية المستخدم إلى مسؤول
4. اختبار إضافة وتعديل وحذف البيانات

## استكشاف الأخطاء

### خطأ في الصلاحيات
- تأكد من تفعيل RLS
- تحقق من وجود المستخدم في جدول user_profiles
- تأكد من صحة دور المستخدم

### خطأ في الاتصال
- تحقق من صحة URL و API Key
- تأكد من تفعيل الخدمة في Supabase

### خطأ في البيانات
- تحقق من صحة البيانات المدخلة
- تأكد من عدم تكرار الرقم القومي

## إصلاح مشكلة Infinite Recursion

إذا واجهت خطأ "infinite recursion detected in policy":

### 1. تطبيق الإصلاحات
قم بتنفيذ محتوى ملف `fix_policies.sql` في SQL Editor

### 2. إنشاء مستخدم Admin
```sql
-- استبدل البريد الإلكتروني بالبريد المطلوب
SELECT public.make_user_admin('<EMAIL>');
```

### 3. التحقق من النجاح
```sql
-- تحقق من أن السياسات تم إنشاؤها
SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public';
```

### 4. إعادة تشغيل التطبيق
بعد تطبيق الإصلاحات، أعد تشغيل التطبيق وجرب تسجيل الدخول مرة أخرى.
