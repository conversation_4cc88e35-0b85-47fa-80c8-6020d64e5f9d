import 'package:flutter/material.dart';
import 'dart:async';

enum SearchFilter { all, name, nationalId, committee }

class AdvancedSearchBar extends StatefulWidget {
  final TextEditingController controller;
  final Function(String, SearchFilter) onSearch;
  final String hint;
  final Duration debounceTime;

  const AdvancedSearchBar({
    super.key,
    required this.controller,
    required this.onSearch,
    required this.hint,
    this.debounceTime = const Duration(milliseconds: 500),
  });

  @override
  State<AdvancedSearchBar> createState() => _AdvancedSearchBarState();
}

class _AdvancedSearchBarState extends State<AdvancedSearchBar> {
  Timer? _debounceTimer;
  SearchFilter _selectedFilter = SearchFilter.all;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(widget.debounceTime, () {
      widget.onSearch(query, _selectedFilter);
    });
  }

  void _clearSearch() {
    widget.controller.clear();
    setState(() {
      _selectedFilter = SearchFilter.all;
    });
    widget.onSearch('', SearchFilter.all);
  }

  String _getFilterLabel(SearchFilter filter) {
    switch (filter) {
      case SearchFilter.all:
        return 'الكل';
      case SearchFilter.name:
        return 'الاسم';
      case SearchFilter.nationalId:
        return 'الرقم القومي';
      case SearchFilter.committee:
        return 'مكان اللجنة';
    }
  }

  IconData _getFilterIcon(SearchFilter filter) {
    switch (filter) {
      case SearchFilter.all:
        return Icons.all_inclusive;
      case SearchFilter.name:
        return Icons.person;
      case SearchFilter.nationalId:
        return Icons.badge;
      case SearchFilter.committee:
        return Icons.location_on;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: widget.controller,
                  onChanged: _onSearchChanged,
                  decoration: InputDecoration(
                    hintText: widget.hint,
                    hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
                    prefixIcon: Icon(
                      Icons.search,
                      color: Theme.of(context).primaryColor,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        bottomLeft: Radius.circular(16),
                      ),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        bottomLeft: Radius.circular(16),
                      ),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        bottomLeft: Radius.circular(16),
                      ),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
              
              // Filter Dropdown
              Container(
                height: 48,
                decoration: BoxDecoration(
                  color: _selectedFilter != SearchFilter.all 
                      ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                      : Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                ),
                child: PopupMenuButton<SearchFilter>(
                  padding: EdgeInsets.zero,
                  icon: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getFilterIcon(_selectedFilter),
                          size: 18,
                          color: _selectedFilter != SearchFilter.all 
                              ? Theme.of(context).primaryColor 
                              : Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getFilterLabel(_selectedFilter),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: _selectedFilter != SearchFilter.all 
                                ? Theme.of(context).primaryColor 
                                : Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.arrow_drop_down,
                          size: 16,
                          color: _selectedFilter != SearchFilter.all 
                              ? Theme.of(context).primaryColor 
                              : Colors.grey[600],
                        ),
                      ],
                    ),
                  ),
                  onSelected: (filter) {
                    setState(() {
                      _selectedFilter = filter;
                    });
                    widget.onSearch(widget.controller.text, filter);
                  },
                  itemBuilder: (context) => SearchFilter.values.map((filter) {
                    return PopupMenuItem<SearchFilter>(
                      value: filter,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Icon(
                              _getFilterIcon(filter),
                              size: 18,
                              color: _selectedFilter == filter 
                                  ? Theme.of(context).primaryColor 
                                  : Colors.grey[600],
                            ),
                            const SizedBox(width: 12),
                            Text(
                              _getFilterLabel(filter),
                              style: TextStyle(
                                color: _selectedFilter == filter 
                                    ? Theme.of(context).primaryColor 
                                    : Colors.black87,
                                fontWeight: _selectedFilter == filter 
                                    ? FontWeight.w600 
                                    : FontWeight.normal,
                              ),
                            ),
                            const Spacer(),
                            if (_selectedFilter == filter)
                              Icon(
                                Icons.check,
                                size: 18,
                                color: Theme.of(context).primaryColor,
                              ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
              
              // Clear Button
              if (widget.controller.text.isNotEmpty || _selectedFilter != SearchFilter.all)
                Container(
                  height: 48,
                  padding: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: _clearSearch,
                    color: Colors.grey[400],
                    iconSize: 20,
                  ),
                ),
            ],
          ),
        ),
        
        // Active Filter Indicator
        if (_selectedFilter != SearchFilter.all || widget.controller.text.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getFilterIcon(_selectedFilter),
                        size: 14,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'البحث في: ${_getFilterLabel(_selectedFilter)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.controller.text.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.orange.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      '"${widget.controller.text}"',
                      style: const TextStyle(
                        fontSize: 11,
                        color: Colors.orange,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
      ],
    );
  }
}
