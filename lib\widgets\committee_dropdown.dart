import 'package:flutter/material.dart';

class CommitteeDropdown extends StatefulWidget {
  final TextEditingController controller;
  final bool enabled;

  const CommitteeDropdown({
    super.key,
    required this.controller,
    this.enabled = true,
  });

  @override
  State<CommitteeDropdown> createState() => _CommitteeDropdownState();
}

class _CommitteeDropdownState extends State<CommitteeDropdown> {
  // قائمة أماكن اللجان المتاحة
  static const List<String> _committees = [
    'مدرسة الشهيد أحمد زويل الابتدائية',
    'مدرسة النصر الإعدادية',
    'مدرسة الحرية الثانوية',
    'مدرسة الأمل الابتدائية',
    'مدرسة المستقبل الإعدادية',
    'مدرسة التحرير الثانوية',
    'مدرسة الوحدة الابتدائية',
    'مدرسة السلام الإعدادية',
    'مدرسة النهضة الثانوية',
    'مدرسة الفجر الابتدائية',
  ];

  String? _selectedCommittee;
  bool _isCustom = false;

  @override
  void initState() {
    super.initState();
    if (widget.controller.text.isNotEmpty) {
      if (_committees.contains(widget.controller.text)) {
        _selectedCommittee = widget.controller.text;
      } else {
        _isCustom = true;
      }
    }
  }

  void _onCommitteeChanged(String? value) {
    setState(() {
      if (value == 'custom') {
        _isCustom = true;
        _selectedCommittee = null;
        widget.controller.clear();
      } else {
        _isCustom = false;
        _selectedCommittee = value;
        widget.controller.text = value ?? '';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مكان اللجنة *',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: widget.enabled ? Colors.grey[700] : Colors.grey[400],
          ),
        ),
        const SizedBox(height: 8),

        if (!_isCustom) ...[
          // Dropdown for predefined committees
          DropdownButtonFormField<String>(
            value: _selectedCommittee,
            onChanged: widget.enabled ? _onCommitteeChanged : null,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى اختيار مكان اللجنة';
              }
              return null;
            },
            decoration: InputDecoration(
              hintText: 'اختر مكان اللجنة',
              hintStyle: TextStyle(color: Colors.grey[400]),
              prefixIcon: Icon(
                Icons.location_on_outlined,
                color: widget.enabled
                    ? Theme.of(context).primaryColor
                    : Colors.grey[400],
              ),
              filled: true,
              fillColor: widget.enabled ? Colors.grey[50] : Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Theme.of(context).primaryColor,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            items: [
              ..._committees.map(
                (committee) => DropdownMenuItem(
                  value: committee,
                  child: Text(committee, style: const TextStyle(fontSize: 14)),
                ),
              ),
              const DropdownMenuItem(
                value: 'custom',
                child: Row(
                  children: [
                    Icon(Icons.add, size: 18),
                    SizedBox(width: 8),
                    Text('إضافة مكان جديد'),
                  ],
                ),
              ),
            ],
          ),
        ] else ...[
          // Text field for custom committee
          TextFormField(
            controller: widget.controller,
            enabled: widget.enabled,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال مكان اللجنة';
              }
              return null;
            },
            decoration: InputDecoration(
              hintText: 'أدخل مكان اللجنة',
              hintStyle: TextStyle(color: Colors.grey[400]),
              prefixIcon: Icon(
                Icons.location_on_outlined,
                color: widget.enabled
                    ? Theme.of(context).primaryColor
                    : Colors.grey[400],
              ),
              suffixIcon: widget.enabled
                  ? IconButton(
                      icon: const Icon(Icons.list),
                      onPressed: () {
                        setState(() {
                          _isCustom = false;
                          widget.controller.clear();
                        });
                      },
                      tooltip: 'اختيار من القائمة',
                    )
                  : null,
              filled: true,
              fillColor: widget.enabled ? Colors.grey[50] : Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Theme.of(context).primaryColor,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
