import 'package:supabase_flutter/supabase_flutter.dart';

// ملف اختبار للتأكد من اتصال Supabase
// شغله بـ: dart test_supabase_connection.dart

void main() async {
  // استبدل هذه القيم بقيمك الحقيقية
  const supabaseUrl = 'YOUR_SUPABASE_URL';
  const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';

  try {
    // تهيئة Supabase
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
    );

    final supabase = Supabase.instance.client;

    print('🔄 اختبار الاتصال بـ Supabase...');

    // اختبار قراءة البيانات
    final response = await supabase
        .from('voters')
        .select('count')
        .count(CountOption.exact);

    print('✅ تم الاتصال بنجاح!');
    print('📊 عدد الناخبين في قاعدة البيانات: ${response.count}');

    // اختبار إدراج بيانات تجريبية
    try {
      await supabase.from('voters').insert({
        'name': 'اختبار الاتصال',
        'national_id': '99999999999999',
        'committee_location': 'لجنة اختبار',
      });
      print('✅ تم إدراج البيانات التجريبية بنجاح');

      // حذف البيانات التجريبية
      await supabase
          .from('voters')
          .delete()
          .eq('national_id', '99999999999999');
      print('✅ تم حذف البيانات التجريبية بنجاح');
    } catch (e) {
      print('⚠️ تحذير: لم يتم إدراج البيانات التجريبية - $e');
    }

    print('\n🎉 Supabase جاهز للاستخدام!');
  } catch (e) {
    print('❌ خطأ في الاتصال: $e');
    print('\n🔧 تأكد من:');
    print('1. صحة URL و API Key');
    print('2. إنشاء الجداول المطلوبة');
    print('3. تفعيل RLS والسياسات');
  }
}
