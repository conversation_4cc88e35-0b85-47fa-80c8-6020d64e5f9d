// مثال لاستخدام Environment Variables في الإنتاج
class SupabaseConfig {
  static String get supabaseUrl {
    // في الإنتاج، استخدم environment variables
    const url = String.fromEnvironment(
      'https://wcsyrjvswgpdicuqdofr.supabase.co',
    );
    if (url.isEmpty) {
      throw Exception('SUPABASE_URL environment variable is not set');
    }
    return url;
  }

  static String get supabaseAnonKey {
    const key = String.fromEnvironment(
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indjc3lyanZzd2dwZGljdXFkb2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyMzYzNTMsImV4cCI6MjA2OTgxMjM1M30.7DyUxaJKKADg7XzGqADNzGGe2Utq3_uuqdrWNbpvn60',
    );
    if (key.isEmpty) {
      throw Exception('SUPABASE_ANON_KEY environment variable is not set');
    }
    return key;
  }
}

// لتشغيل التطبيق مع environment variables:
// flutter run --dart-define=SUPABASE_URL=your_url --dart-define=SUPABASE_ANON_KEY=your_key
