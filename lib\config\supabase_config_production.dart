// مثال لاستخدام Environment Variables في الإنتاج
class SupabaseConfig {
  static String get supabaseUrl {
    // في الإنتاج، استخدم environment variables
    const url = String.fromEnvironment('SUPABASE_URL');
    if (url.isEmpty) {
      throw Exception('SUPABASE_URL environment variable is not set');
    }
    return url;
  }

  static String get supabaseAnonKey {
    const key = String.fromEnvironment('SUPABASE_ANON_KEY');
    if (key.isEmpty) {
      throw Exception('SUPABASE_ANON_KEY environment variable is not set');
    }
    return key;
  }
}

// لتشغيل التطبيق مع environment variables:
// flutter run --dart-define=SUPABASE_URL=your_url --dart-define=SUPABASE_ANON_KEY=your_key
