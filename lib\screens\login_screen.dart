import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';

import '../providers/auth_provider.dart';
import '../utils/validators.dart';
import '../utils/responsive_helper.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import '../config/app_router.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailOrPhoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailOrPhoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validateEmailOrPhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني أو رقم الهاتف';
    }

    final trimmedValue = value.trim();

    // Check if it's an email
    if (trimmedValue.contains('@')) {
      return Validators.email(trimmedValue);
    }

    // Check if it's a phone number (simple validation)
    if (trimmedValue.length < 10) {
      return 'رقم الهاتف يجب أن يكون 10 أرقام على الأقل';
    }

    // Check if it contains only numbers and some allowed characters
    final phoneRegex = RegExp(r'^[\d\+\-\(\)\s]+$');
    if (!phoneRegex.hasMatch(trimmedValue)) {
      return 'تنسيق البريد الإلكتروني أو رقم الهاتف غير صحيح';
    }

    return null;
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    final success = await ref
        .read(authProvider.notifier)
        .signIn(
          emailOrPhone: _emailOrPhoneController.text.trim(),
          password: _passwordController.text,
        );

    if (success && mounted) {
      context.goToHome();
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Listen to auth errors
    ref.listen<AuthState>(authProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
        // Clear error after showing
        Future.delayed(const Duration(seconds: 3), () {
          ref.read(authProvider.notifier).clearError();
        });
      }
    });

    return Scaffold(
      body: SafeArea(
        child: ResponsiveHelper.buildResponsiveContainer(
          context: context,
          maxWidth: ResponsiveHelper.isMobile(context) ? double.infinity : 500,
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: ResponsiveHelper.getSpacing(context, 60)),

                  // Logo/Title
                  Container(
                    padding: EdgeInsets.all(
                      ResponsiveHelper.getSpacing(context, 20),
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).primaryColor.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      FontAwesomeIcons.checkToSlot,
                      size: ResponsiveHelper.getIconSize(context, 60),
                      color: Theme.of(context).primaryColor,
                    ),
                  ),

                  const SizedBox(height: 32),

                  Text(
                    'نظام إدارة الانتخابات',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),

                  Text(
                    'تسجيل الدخول للمتابعة',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 48),

                  // Email or Phone Field
                  CustomTextField(
                    controller: _emailOrPhoneController,
                    label: 'البريد الإلكتروني أو رقم الهاتف',
                    hint: 'أدخل البريد الإلكتروني أو رقم الهاتف',
                    prefixIcon: Icons.person_outline,
                    keyboardType: TextInputType.text,
                    validator: _validateEmailOrPhone,
                    enabled: !authState.isLoading,
                  ),

                  const SizedBox(height: 16),

                  // Password Field
                  CustomTextField(
                    controller: _passwordController,
                    label: 'كلمة المرور',
                    hint: 'أدخل كلمة المرور',
                    prefixIcon: Icons.lock_outline,
                    obscureText: _obscurePassword,
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    validator: Validators.password,
                    enabled: !authState.isLoading,
                  ),

                  const SizedBox(height: 32),

                  // Login Button
                  CustomButton(
                    onPressed: authState.isLoading ? null : _handleLogin,
                    child: authState.isLoading
                        ? const SpinKitThreeBounce(
                            color: Colors.white,
                            size: 20,
                          )
                        : const Text(
                            'تسجيل الدخول',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),

                  const SizedBox(height: 24),

                  // Sign Up Button
                  TextButton(
                    onPressed: authState.isLoading
                        ? null
                        : () {
                            context.goToSignUp();
                          },
                    child: const Text('ليس لديك حساب؟ إنشاء حساب جديد'),
                  ),

                  const SizedBox(height: 24),

                  // Help Info
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.green.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.help_outline,
                              color: Colors.green[700],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'مساعدة',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'يمكنك تسجيل الدخول باستخدام البريد الإلكتروني أو رقم الهاتف',
                          style: TextStyle(
                            color: Colors.green[600],
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'إذا لم يكن لديك حساب، يمكنك إنشاء حساب جديد',
                          style: TextStyle(
                            color: Colors.green[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
