-- ===================================
-- إعداد قاعدة البيانات لتطبيق إدارة الانتخابات
-- ===================================

-- إنشاء جدول معلومات المستخدمين
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  email TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إن<PERSON><PERSON><PERSON> جدول بيانات الناخبين
CREATE TABLE IF NOT EXISTS voters (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  national_id TEXT UNIQUE NOT NULL,
  committee_location TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_voters_name ON voters(name);
CREATE INDEX IF NOT EXISTS idx_voters_national_id ON voters(national_id);
CREATE INDEX IF NOT EXISTS idx_voters_committee ON voters(committee_location);
CREATE INDEX IF NOT EXISTS idx_voters_created_at ON voters(created_at);

-- فهارس لجدول المستخدمين
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

-- إنشاء فهرس للبحث النصي
CREATE INDEX IF NOT EXISTS idx_voters_search ON voters USING gin(
  to_tsvector('arabic', name || ' ' || national_id || ' ' || committee_location)
);

-- تفعيل Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE voters ENABLE ROW LEVEL SECURITY;

-- ===================================
-- سياسات الأمان لجدول user_profiles
-- ===================================

-- السماح للمستخدمين بقراءة ملفهم الشخصي فقط
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT 
  TO authenticated 
  USING (auth.uid() = id);

-- السماح للمستخدمين بتحديث ملفهم الشخصي فقط
CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE 
  TO authenticated 
  USING (auth.uid() = id);

-- السماح بإنشاء ملف شخصي جديد عند التسجيل
CREATE POLICY "Users can insert own profile" ON user_profiles
  FOR INSERT 
  TO authenticated 
  WITH CHECK (auth.uid() = id);

-- ===================================
-- سياسات الأمان لجدول voters
-- ===================================

-- السماح لجميع المستخدمين المصادق عليهم بقراءة بيانات الناخبين
CREATE POLICY "Authenticated users can view voters" ON voters
  FOR SELECT 
  TO authenticated 
  USING (true);

-- السماح لجميع المستخدمين المصادق عليهم بإضافة ناخبين جدد
CREATE POLICY "Authenticated users can insert voters" ON voters
  FOR INSERT 
  TO authenticated 
  WITH CHECK (true);

-- السماح لجميع المستخدمين المصادق عليهم بتحديث بيانات الناخبين
CREATE POLICY "Authenticated users can update voters" ON voters
  FOR UPDATE 
  TO authenticated 
  USING (true);

-- السماح لجميع المستخدمين المصادق عليهم بحذف بيانات الناخبين
CREATE POLICY "Authenticated users can delete voters" ON voters
  FOR DELETE 
  TO authenticated 
  USING (true);

-- ===================================
-- دوال مساعدة
-- ===================================

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء trigger لتحديث updated_at عند تعديل بيانات الناخبين
CREATE TRIGGER update_voters_updated_at 
  BEFORE UPDATE ON voters 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- دالة للبحث في بيانات الناخبين
CREATE OR REPLACE FUNCTION search_voters(search_term TEXT)
RETURNS TABLE (
  id UUID,
  name TEXT,
  national_id TEXT,
  committee_location TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT v.id, v.name, v.national_id, v.committee_location, v.created_at, v.updated_at
  FROM voters v
  WHERE 
    v.name ILIKE '%' || search_term || '%' OR
    v.national_id ILIKE '%' || search_term || '%' OR
    v.committee_location ILIKE '%' || search_term || '%'
  ORDER BY v.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على إحصائيات
CREATE OR REPLACE FUNCTION get_voters_stats()
RETURNS TABLE (
  total_voters BIGINT,
  voters_today BIGINT,
  voters_this_week BIGINT,
  voters_this_month BIGINT,
  committees_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_voters,
    COUNT(*) FILTER (WHERE DATE(created_at) = CURRENT_DATE) as voters_today,
    COUNT(*) FILTER (WHERE created_at >= DATE_TRUNC('week', CURRENT_DATE)) as voters_this_week,
    COUNT(*) FILTER (WHERE created_at >= DATE_TRUNC('month', CURRENT_DATE)) as voters_this_month,
    COUNT(DISTINCT committee_location) as committees_count
  FROM voters;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- بيانات تجريبية (اختيارية)
-- ===================================

-- إدراج مستخدمين تجريبيين
-- ملاحظة: يجب إنشاء هؤلاء المستخدمين في Supabase Auth أولاً
-- ثم إضافة بياناتهم هنا مع نفس UUID

-- مثال لإدراج مستخدم مسؤول (استبدل UUID بالقيمة الفعلية من Supabase Auth)
-- INSERT INTO user_profiles (id, username, email, role) VALUES
--   ('00000000-0000-0000-0000-000000000001', 'admin', '<EMAIL>', 'admin'),
--   ('00000000-0000-0000-0000-000000000002', 'user', '<EMAIL>', 'user')
-- ON CONFLICT (id) DO NOTHING;

-- إدراج بعض أماكن اللجان كأمثلة
INSERT INTO voters (name, national_id, committee_location) VALUES
  ('أحمد محمد علي', '29012011234567', 'مدرسة الشهيد أحمد زويل الابتدائية'),
  ('فاطمة أحمد حسن', '29505021234568', 'مدرسة النصر الإعدادية'),
  ('محمد عبد الله محمود', '28803151234569', 'مدرسة الحرية الثانوية'),
  ('عائشة محمد إبراهيم', '29201101234570', 'مدرسة الأمل الابتدائية'),
  ('يوسف أحمد عبد الرحمن', '28706201234571', 'مدرسة المستقبل الإعدادية')
ON CONFLICT (national_id) DO NOTHING;

-- ===================================
-- إعدادات إضافية للأمان
-- ===================================

-- منع حذف جدول المستخدمين
REVOKE DELETE ON user_profiles FROM anon, authenticated;

-- السماح بالقراءة فقط لجدول المستخدمين للمستخدمين العاديين
-- (المسؤولون يمكنهم التعديل من خلال التطبيق)

-- إنشاء دور خاص للمسؤولين (اختياري)
-- CREATE ROLE admin_role;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO admin_role;

-- ===================================
-- تحسينات الأداء
-- ===================================

-- تحليل الجداول لتحسين الاستعلامات
ANALYZE user_profiles;
ANALYZE voters;

-- إعداد إحصائيات للاستعلامات
ALTER TABLE voters SET (autovacuum_analyze_scale_factor = 0.02);
ALTER TABLE user_profiles SET (autovacuum_analyze_scale_factor = 0.02);

-- ===================================
-- رسائل التأكيد
-- ===================================

DO $$
BEGIN
  RAISE NOTICE 'تم إعداد قاعدة البيانات بنجاح!';
  RAISE NOTICE 'الجداول المنشأة: user_profiles, voters';
  RAISE NOTICE 'السياسات الأمنية: مفعلة';
  RAISE NOTICE 'الفهارس: منشأة';
  RAISE NOTICE 'الدوال المساعدة: منشأة';
  RAISE NOTICE 'البيانات التجريبية: مدرجة';
END $$;
