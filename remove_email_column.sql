-- ===================================
-- حذف عمود البريد الإلكتروني من قاعدة البيانات
-- ===================================

-- تحذير: هذا الملف سيحذف عمود البريد الإلكتروني نهائياً
-- تأكد من عمل نسخة احتياطية قبل التشغيل

-- الخطوة 1: حذف الفهرس المرتبط بالبريد الإلكتروني
DROP INDEX IF EXISTS idx_user_profiles_email;

-- الخطوة 2: حذف عمود البريد الإلكتروني
ALTER TABLE user_profiles DROP COLUMN IF EXISTS email;

-- الخطوة 3: التحقق من البنية الجديدة للجدول
DO $$
BEGIN
  -- عرض أعمدة الجدول الحالية
  RAISE NOTICE 'أعمدة جدول user_profiles بعد الحذف:';
  
  -- التحقق من وجود الأعمدة المطلوبة
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'id'
  ) THEN
    RAISE NOTICE '✅ عمود id موجود';
  ELSE
    RAISE NOTICE '❌ عمود id مفقود';
  END IF;
  
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'username'
  ) THEN
    RAISE NOTICE '✅ عمود username موجود';
  ELSE
    RAISE NOTICE '❌ عمود username مفقود';
  END IF;
  
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'role'
  ) THEN
    RAISE NOTICE '✅ عمود role موجود';
  ELSE
    RAISE NOTICE '❌ عمود role مفقود';
  END IF;
  
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'created_at'
  ) THEN
    RAISE NOTICE '✅ عمود created_at موجود';
  ELSE
    RAISE NOTICE '❌ عمود created_at مفقود';
  END IF;
  
  -- التحقق من حذف عمود البريد الإلكتروني
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'email'
  ) THEN
    RAISE NOTICE '✅ تم حذف عمود email بنجاح';
  ELSE
    RAISE NOTICE '❌ عمود email لا يزال موجود';
  END IF;
  
  -- عرض عدد المستخدمين الحاليين
  DECLARE
    user_count INTEGER;
  BEGIN
    SELECT COUNT(*) INTO user_count FROM user_profiles;
    RAISE NOTICE 'عدد المستخدمين الحاليين: %', user_count;
  END;
  
  RAISE NOTICE '=== تم حذف عمود البريد الإلكتروني بنجاح! ===';
END $$;

-- ===================================
-- ملاحظات مهمة
-- ===================================

-- 1. هذا التغيير لا يمكن التراجع عنه
-- 2. تأكد من تحديث التطبيق قبل تشغيل هذا الملف
-- 3. جميع البيانات المرتبطة بالبريد الإلكتروني ستُحذف
-- 4. المستخدمون الحاليون سيحتفظون بأسماء المستخدمين فقط
-- 5. تسجيل الدخول سيتم بـ username بدلاً من email

-- ===================================
-- البنية الجديدة للجدول
-- ===================================

-- user_profiles:
-- - id (UUID, PRIMARY KEY)
-- - username (TEXT, UNIQUE, NOT NULL)
-- - role (TEXT, NOT NULL, DEFAULT 'user')
-- - is_admin_created (BOOLEAN, DEFAULT FALSE)
-- - created_at (TIMESTAMP WITH TIME ZONE, DEFAULT NOW())

-- ===================================
-- خطوات ما بعد التشغيل
-- ===================================

-- 1. تأكد من أن التطبيق يعمل بشكل صحيح
-- 2. اختبر تسجيل الدخول بـ username
-- 3. اختبر إضافة مستخدمين جدد
-- 4. تأكد من عمل جميع المميزات

RAISE NOTICE 'تم الانتهاء من حذف عمود البريد الإلكتروني!';
