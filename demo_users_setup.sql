-- ===================================
-- إعداد المستخدمين التجريبيين
-- ===================================

-- تعليمات مهمة:
-- 1. يجب إنشاء المستخدمين في Supabase Auth Dashboard أولاً
-- 2. انسخ User IDs الفعلية من Authentication > Users
-- 3. استبدل الـ UUIDs أدناه بالقيم الحقيقية
-- 4. ثم شغّل هذا الملف

-- ===================================
-- خطوات إنشاء المستخدمين التجريبيين:
-- ===================================

-- الخطوة 1: في Supabase Dashboard
-- اذهب إلى Authentication > Users > Add User

-- المستخدم المسؤول:
-- Email: <EMAIL>
-- Password: 123456789
-- Auto Confirm User: ✅

-- المستخدم العادي:
-- Email: <EMAIL>
-- Password: 123456789
-- Auto Confirm User: ✅

-- الخطوة 2: انسخ User IDs
-- بعد إنشاء المستخدمين، انسخ User UID لكل مستخدم

-- الخطوة 3: استبدل UUIDs أدناه
-- استبدل 'ADMIN_USER_ID_FROM_SUPABASE' و 'REGULAR_USER_ID_FROM_SUPABASE'
-- بالـ UUIDs الحقيقية

-- ===================================
-- إدراج بيانات المستخدمين
-- ===================================

-- استبدل هذه UUIDs بالقيم الحقيقية من Supabase Auth
INSERT INTO user_profiles (id, username, email, role) VALUES
  ('ADMIN_USER_ID_FROM_SUPABASE', 'admin', '<EMAIL>', 'admin'),
  ('REGULAR_USER_ID_FROM_SUPABASE', 'user', '<EMAIL>', 'user')
ON CONFLICT (id) DO NOTHING;

-- ===================================
-- مثال بـ UUIDs حقيقية (للمرجع فقط)
-- ===================================

-- مثال: إذا كانت UUIDs الحقيقية هي:
-- Admin: a1b2c3d4-e5f6-7890-abcd-ef1234567890
-- User: b2c3d4e5-f6g7-8901-bcde-f23456789012

-- فسيكون الكود:
-- INSERT INTO user_profiles (id, username, email, role) VALUES
--   ('a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'admin', '<EMAIL>', 'admin'),
--   ('b2c3d4e5-f6g7-8901-bcde-f23456789012', 'user', '<EMAIL>', 'user')
-- ON CONFLICT (id) DO NOTHING;

-- ===================================
-- التحقق من النجاح
-- ===================================

-- للتحقق من إضافة المستخدمين بنجاح:
-- SELECT * FROM user_profiles;

-- ===================================
-- رسائل التأكيد
-- ===================================

DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM user_profiles WHERE username = 'admin') THEN
    RAISE NOTICE 'تم إضافة المستخدم المسؤول بنجاح!';
  ELSE
    RAISE NOTICE 'تحذير: لم يتم إضافة المستخدم المسؤول. تأكد من استبدال UUID الصحيح.';
  END IF;
  
  IF EXISTS (SELECT 1 FROM user_profiles WHERE username = 'user') THEN
    RAISE NOTICE 'تم إضافة المستخدم العادي بنجاح!';
  ELSE
    RAISE NOTICE 'تحذير: لم يتم إضافة المستخدم العادي. تأكد من استبدال UUID الصحيح.';
  END IF;
  
  RAISE NOTICE 'عدد المستخدمين المضافين: %', (SELECT COUNT(*) FROM user_profiles);
END $$;
