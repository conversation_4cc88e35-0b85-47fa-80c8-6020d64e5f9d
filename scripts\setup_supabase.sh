#!/bin/bash

# ===================================
# سكريبت إعداد Supabase التلقائي
# ===================================

echo "🚀 بدء إعداد Supabase لتطبيق إدارة الانتخابات..."

# التحقق من وجود Supabase CLI
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI غير مثبت. يرجى تثبيته أولاً:"
    echo "npm install -g supabase"
    echo "أو"
    echo "brew install supabase/tap/supabase"
    exit 1
fi

# التحقق من تسجيل الدخول
echo "🔐 التحقق من تسجيل الدخول..."
if ! supabase projects list &> /dev/null; then
    echo "يرجى تسجيل الدخول إلى Supabase أولاً:"
    echo "supabase login"
    exit 1
fi

# قراءة معرف المشروع
read -p "📝 أدخل معرف مشروع Supabase (Project ID): " PROJECT_ID

if [ -z "$PROJECT_ID" ]; then
    echo "❌ معرف المشروع مطلوب!"
    exit 1
fi

# ربط المشروع المحلي
echo "🔗 ربط المشروع المحلي..."
supabase link --project-ref $PROJECT_ID

# تطبيق ملف SQL
echo "📊 إنشاء قاعدة البيانات..."
if [ -f "database_setup.sql" ]; then
    supabase db push
    echo "✅ تم تطبيق إعدادات قاعدة البيانات"
else
    echo "⚠️  ملف database_setup.sql غير موجود"
    echo "يرجى تشغيل الأوامر SQL يدوياً من لوحة تحكم Supabase"
fi

# الحصول على مفاتيح API
echo "🔑 الحصول على مفاتيح API..."
PROJECT_URL="https://$PROJECT_ID.supabase.co"
echo "Project URL: $PROJECT_URL"

# إنشاء ملف الإعدادات
echo "⚙️  إنشاء ملف الإعدادات..."
cat > lib/config/supabase_config.dart << EOF
class SupabaseConfig {
  // يجب استبدال هذه القيم بقيم مشروعك الفعلية
  static const String supabaseUrl = '$PROJECT_URL';
  static const String supabaseAnonKey = 'YOUR_ANON_KEY_HERE';
  
  // للحصول على المفاتيح:
  // 1. اذهب إلى لوحة تحكم Supabase
  // 2. Settings > API
  // 3. انسخ Project URL و anon public key
}
EOF

echo "📋 إنشاء مستخدم مسؤول..."
echo "يرجى اتباع الخطوات التالية يدوياً:"
echo "1. اذهب إلى لوحة تحكم Supabase"
echo "2. Authentication > Users"
echo "3. أضف مستخدم جديد"
echo "4. انسخ User ID"
echo "5. اذهب إلى Table Editor > user_profiles"
echo "6. أضف سجل جديد بـ role = 'admin'"

echo ""
echo "✅ تم إعداد Supabase بنجاح!"
echo ""
echo "الخطوات التالية:"
echo "1. احصل على مفاتيح API من لوحة التحكم"
echo "2. حدث ملف lib/config/supabase_config.dart"
echo "3. أنشئ مستخدم مسؤول"
echo "4. شغّل التطبيق: flutter run"
echo ""
echo "🎉 استمتع بتطبيقك!"
