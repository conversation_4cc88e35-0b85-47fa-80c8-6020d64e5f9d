-- =====================================================
-- Fix Supabase RLS Policies - Remove Infinite Recursion
-- =====================================================

-- First, drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Authenticated users can view voters" ON public.voters;
DROP POLICY IF EXISTS "Admins can insert voters" ON public.voters;
DROP POLICY IF EXISTS "Admins can update voters" ON public.voters;
DROP POLICY IF EXISTS "Admins can delete voters" ON public.voters;
DROP POLICY IF EXISTS "Authenticated users can view committee locations" ON public.committee_locations;
DROP POLICY IF EXISTS "Ad<PERSON> can modify committee locations" ON public.committee_locations;

-- =====================================================
-- User Profiles Policies (Simplified)
-- =====================================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Allow insert for new users (handled by trigger)
CREATE POLICY "Allow insert for authenticated users" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- =====================================================
-- Voters Policies (Simplified)
-- =====================================================

-- All authenticated users can view voters
CREATE POLICY "Authenticated users can view voters" ON public.voters
    FOR SELECT USING (auth.role() = 'authenticated');

-- Only authenticated users can insert voters (we'll check admin role in app)
CREATE POLICY "Authenticated users can insert voters" ON public.voters
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Only authenticated users can update voters (we'll check admin role in app)
CREATE POLICY "Authenticated users can update voters" ON public.voters
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Only authenticated users can delete voters (we'll check admin role in app)
CREATE POLICY "Authenticated users can delete voters" ON public.voters
    FOR DELETE USING (auth.role() = 'authenticated');

-- =====================================================
-- Committee Locations Policies (Simplified)
-- =====================================================

-- All authenticated users can view committee locations
CREATE POLICY "Authenticated users can view committee locations" ON public.committee_locations
    FOR SELECT USING (auth.role() = 'authenticated');

-- All authenticated users can insert committee locations (we'll check admin role in app)
CREATE POLICY "Authenticated users can insert committee locations" ON public.committee_locations
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- All authenticated users can update committee locations (we'll check admin role in app)
CREATE POLICY "Authenticated users can update committee locations" ON public.committee_locations
    FOR UPDATE USING (auth.role() = 'authenticated');

-- All authenticated users can delete committee locations (we'll check admin role in app)
CREATE POLICY "Authenticated users can delete committee locations" ON public.committee_locations
    FOR DELETE USING (auth.role() = 'authenticated');

-- =====================================================
-- Update the trigger function to be simpler
-- =====================================================

-- Drop and recreate the user creation function
DROP FUNCTION IF EXISTS public.handle_new_user();

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, role, created_at, updated_at)
    VALUES (
        NEW.id, 
        NEW.email, 
        'user',
        NOW(),
        NOW()
    );
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- If insert fails, just return NEW to not block user creation
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- Grant permissions
-- =====================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO anon, authenticated;

-- Grant permissions on tables
GRANT ALL ON public.user_profiles TO authenticated;
GRANT ALL ON public.voters TO authenticated;
GRANT ALL ON public.committee_locations TO authenticated;

-- Grant permissions on sequences
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =====================================================
-- Create a simple admin user function
-- =====================================================

CREATE OR REPLACE FUNCTION public.make_user_admin(user_email TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_profiles 
    SET role = 'admin', updated_at = NOW()
    WHERE email = user_email;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- Create a function to check if user is admin
-- =====================================================

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.user_profiles 
        WHERE id = auth.uid() AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
