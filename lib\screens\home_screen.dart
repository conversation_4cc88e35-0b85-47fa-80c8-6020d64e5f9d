import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/auth_provider.dart';
import '../providers/voters_provider.dart';
import '../models/voter_model.dart';
import '../widgets/voter_card.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/floating_action_menu.dart';
import 'voter_form_screen.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(votersProvider.notifier).loadVoters(refresh: true);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      ref.read(votersProvider.notifier).loadVoters();
    }
  }

  void _onSearch(String query) {
    ref.read(votersProvider.notifier).searchVoters(query);
  }

  Future<void> _onRefresh() async {
    await ref.read(votersProvider.notifier).loadVoters(refresh: true);
  }

  void _showVoterForm({VoterModel? voter}) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VoterFormScreen(voter: voter),
        fullscreenDialog: true,
      ),
    );
  }

  Future<void> _confirmDelete(VoterModel voter) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف "${voter.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(votersProvider.notifier)
          .deleteVoter(voter.id!);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final votersState = ref.watch(votersProvider);
    final canAdd = ref.watch(canAddProvider);
    final canEdit = ref.watch(canEditProvider);
    final canDelete = ref.watch(canDeleteProvider);

    // Listen to voters errors
    ref.listen<VotersState>(votersProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
        // Clear error after showing
        Future.delayed(const Duration(seconds: 3), () {
          ref.read(votersProvider.notifier).clearError();
        });
      }
    });

    return Scaffold(
      appBar: CustomAppBar(
        title: 'نظام إدارة الانتخابات',
        user: authState.user,
        onLogout: () async {
          await ref.read(authProvider.notifier).signOut();
          if (mounted) {
            Navigator.of(context).pushReplacementNamed('/login');
          }
        },
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SearchBarWidget(
              controller: _searchController,
              onSearch: _onSearch,
              hint: 'البحث بالاسم أو الرقم القومي أو مكان اللجنة',
            ),
          ),

          // Stats Row
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Text(
                          '${votersState.totalCount}',
                          style: Theme.of(context).textTheme.headlineMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                        ),
                        const Text('إجمالي المسجلين'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Text(
                          authState.user?.role.displayName ?? '',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700],
                              ),
                        ),
                        const Text('نوع المستخدم'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Voters List
          Expanded(
            child: RefreshIndicator(
              onRefresh: _onRefresh,
              child: votersState.voters.isEmpty && !votersState.isLoading
                  ? _buildEmptyState()
                  : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount:
                          votersState.voters.length +
                          (votersState.hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index >= votersState.voters.length) {
                          return const Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Center(
                              child: SpinKitThreeBounce(
                                color: Colors.blue,
                                size: 20,
                              ),
                            ),
                          );
                        }

                        final voter = votersState.voters[index];
                        return VoterCard(
                          voter: voter,
                          onEdit: canEdit
                              ? () => _showVoterForm(voter: voter)
                              : null,
                          onDelete: canDelete
                              ? () => _confirmDelete(voter)
                              : null,
                        );
                      },
                    ),
            ),
          ),
        ],
      ),
      floatingActionButton: canAdd
          ? FloatingActionMenu(onAddPressed: () => _showVoterForm())
          : null,
    );
  }

  Widget _buildEmptyState() {
    final searchQuery = ref.watch(votersSearchQueryProvider);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            searchQuery.isNotEmpty
                ? FontAwesomeIcons.magnifyingGlass
                : FontAwesomeIcons.users,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            searchQuery.isNotEmpty
                ? 'لا توجد نتائج للبحث'
                : 'لا توجد بيانات مسجلة',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            searchQuery.isNotEmpty
                ? 'جرب البحث بكلمات مختلفة'
                : 'ابدأ بإضافة أول مسجل',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }
}
