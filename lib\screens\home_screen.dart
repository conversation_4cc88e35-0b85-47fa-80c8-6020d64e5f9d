import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../providers/auth_provider.dart';
import '../providers/voters_provider.dart';
import '../models/voter_model.dart';
import '../models/user_model.dart';
import '../utils/responsive_helper.dart';
import '../widgets/voter_card.dart';
import '../widgets/advanced_search_bar.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/floating_action_menu.dart';
import '../config/app_router.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(votersProvider.notifier).loadVoters(refresh: true);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      ref.read(votersProvider.notifier).loadVoters();
    }
  }

  void _onSearch(String query, [dynamic filter]) {
    ref.read(votersProvider.notifier).searchVoters(query);
  }

  Future<void> _onRefresh() async {
    await ref.read(votersProvider.notifier).loadVoters(refresh: true);
  }

  void _showVoterForm({VoterModel? voter}) {
    if (voter != null) {
      context.goToEditVoter(voter.id!);
    } else {
      context.goToAddVoter();
    }
  }

  Future<void> _confirmDelete(VoterModel voter) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف "${voter.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(votersProvider.notifier)
          .deleteVoter(voter.id!);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final votersState = ref.watch(votersProvider);
    final canAdd = ref.watch(canAddProvider);
    final canEdit = ref.watch(canEditProvider);
    final canDelete = ref.watch(canDeleteProvider);

    // Listen to voters errors
    ref.listen<VotersState>(votersProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
        // Clear error after showing
        Future.delayed(const Duration(seconds: 3), () {
          ref.read(votersProvider.notifier).clearError();
        });
      }
    });

    return Scaffold(
      appBar: CustomAppBar(
        title: 'نظام إدارة الانتخابات',
        user: authState.user,
        onLogout: () async {
          await ref.read(authProvider.notifier).signOut();
          if (mounted && context.mounted) {
            context.goToLogin();
          }
        },
        onStatistics: () => context.pushStatistics(),
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: AdvancedSearchBar(
              controller: _searchController,
              onSearch: _onSearch,
              hint: 'البحث بالاسم أو الرقم القومي أو مكان اللجنة',
            ),
          ),

          // Stats Row
          Padding(
            padding: ResponsiveHelper.getScreenPadding(context),
            child: ResponsiveHelper.buildResponsiveGrid(
              context: context,
              spacing: ResponsiveHelper.getSpacing(context, 12),
              children: [
                _buildStatCard(
                  title: 'إجمالي المسجلين',
                  value: '${votersState.totalCount}',
                  icon: Icons.people,
                  color: Theme.of(context).primaryColor,
                ),
                _buildStatCard(
                  title: 'نوع المستخدم',
                  value: authState.user?.role.displayName ?? '',
                  icon: authState.user?.role == UserRole.admin
                      ? Icons.admin_panel_settings
                      : Icons.person,
                  color: authState.user?.role == UserRole.admin
                      ? Colors.orange
                      : Colors.green,
                ),
                _buildStatCard(
                  title: 'النتائج المعروضة',
                  value: '${votersState.voters.length}',
                  icon: Icons.visibility,
                  color: Colors.blue,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Loading Indicator for initial load
          if (votersState.isLoading && votersState.voters.isEmpty)
            const Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SpinKitThreeBounce(color: Colors.blue, size: 30),
                    SizedBox(height: 16),
                    Text('جاري تحميل البيانات...'),
                  ],
                ),
              ),
            )
          else
            // Voters List
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: votersState.voters.isEmpty
                    ? _buildEmptyState()
                    : Column(
                        children: [
                          // Results Summary
                          Container(
                            margin: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'عرض ${votersState.voters.length} من أصل ${votersState.totalCount} مسجل',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                if (votersState.hasMore) ...[
                                  const Spacer(),
                                  Text(
                                    'اسحب لأسفل لعرض المزيد',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: Colors.grey[500],
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),

                          // List
                          Expanded(
                            child: ListView.builder(
                              controller: _scrollController,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              itemCount:
                                  votersState.voters.length +
                                  (votersState.hasMore ? 1 : 0),
                              itemBuilder: (context, index) {
                                if (index >= votersState.voters.length) {
                                  return Container(
                                    padding: const EdgeInsets.all(16.0),
                                    child: const Center(
                                      child: Column(
                                        children: [
                                          SpinKitThreeBounce(
                                            color: Colors.blue,
                                            size: 20,
                                          ),
                                          SizedBox(height: 8),
                                          Text(
                                            'جاري تحميل المزيد...',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                }

                                final voter = votersState.voters[index];
                                return VoterCard(
                                  voter: voter,
                                  onEdit: canEdit
                                      ? () => _showVoterForm(voter: voter)
                                      : null,
                                  onDelete: canDelete
                                      ? () => _confirmDelete(voter)
                                      : null,
                                );
                              },
                            ),
                          ),
                        ],
                      ),
              ),
            ),
        ],
      ),
      floatingActionButton: canAdd
          ? FloatingActionMenu(onAddPressed: () => _showVoterForm())
          : null,
    );
  }

  Widget _buildEmptyState() {
    final searchQuery = ref.watch(votersSearchQueryProvider);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            searchQuery.isNotEmpty
                ? FontAwesomeIcons.magnifyingGlass
                : FontAwesomeIcons.users,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            searchQuery.isNotEmpty
                ? 'لا توجد نتائج للبحث'
                : 'لا توجد بيانات مسجلة',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            searchQuery.isNotEmpty
                ? 'جرب البحث بكلمات مختلفة'
                : 'ابدأ بإضافة أول مسجل',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(ResponsiveHelper.getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: ResponsiveHelper.getIconSize(context, 24),
            color: color,
          ),
          SizedBox(height: ResponsiveHelper.getSpacing(context, 8)),
          Text(
            value,
            style: ResponsiveHelper.getTitleStyle(
              context,
            ).copyWith(fontWeight: FontWeight.bold, color: color),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: ResponsiveHelper.getSpacing(context, 4)),
          Text(
            title,
            style: ResponsiveHelper.getBodyStyle(context).copyWith(
              color: Colors.grey[600],
              fontSize: ResponsiveHelper.getFontSize(context, 12),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
