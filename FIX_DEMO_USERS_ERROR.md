# حل خطأ البيانات التجريبية

## 🚨 الخطأ الحالي

```
ERROR: 23503: insert or update on table "user_profiles" violates foreign key constraint "user_profiles_id_fkey"
DETAIL: Key (id)=(00000000-0000-0000-0000-000000000001) is not present in table "users".
```

## 🔍 سبب المشكلة

المشكلة أن الـ UUIDs المستخدمة في البيانات التجريبية (`00000000-0000-0000-0000-000000000001`) غير موجودة في جدول `auth.users` في Supabase.

## ✅ الحل السريع

### الطريقة 1: استخدام ملف demo_users_setup.sql المحدث

1. **تشغيل database_setup.sql أولاً:**
   ```sql
   -- في Supabase SQL Editor
   -- انسخ والصق محتوى database_setup.sql
   -- اضغط Run
   ```

2. **إنشاء المستخدمين في Supabase Auth:**
   - اذهب إلى Authentication > Users > Add User
   - أضف: <EMAIL> / 123456789
   - أضف: <EMAIL> / 123456789

3. **الحصول على User IDs الحقيقية:**
   - انسخ User UID لكل مستخدم من قائمة Users

4. **تحديث demo_users_setup.sql:**
   ```sql
   -- استبدل هذا:
   INSERT INTO user_profiles (id, username, email, role) VALUES
     ('ADMIN_USER_ID_FROM_SUPABASE', 'admin', '<EMAIL>', 'admin'),
     ('REGULAR_USER_ID_FROM_SUPABASE', 'user', '<EMAIL>', 'user')
   
   -- بهذا (مع UUIDs الحقيقية):
   INSERT INTO user_profiles (id, username, email, role) VALUES
     ('a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'admin', '<EMAIL>', 'admin'),
     ('b2c3d4e5-f6g7-8901-bcde-f23456789012', 'user', '<EMAIL>', 'user')
   ```

5. **تشغيل الملف المحدث:**
   ```sql
   -- في SQL Editor
   -- انسخ والصق المحتوى المحدث
   -- اضغط Run
   ```

### الطريقة 2: إضافة يدوية عبر واجهة Supabase

1. **بعد إنشاء المستخدمين في Auth:**
   - اذهب إلى Table Editor > user_profiles
   - اضغط Insert > Insert row

2. **للمستخدم المسؤول:**
   - id: [انسخ User UID من Authentication]
   - username: admin
   - email: <EMAIL>
   - role: admin

3. **للمستخدم العادي:**
   - id: [انسخ User UID من Authentication]
   - username: user
   - email: <EMAIL>
   - role: user

## 🎯 التحقق من النجاح

بعد إضافة المستخدمين، تحقق من النجاح:

```sql
-- في SQL Editor
SELECT * FROM user_profiles;
```

يجب أن ترى:
```
id                                   | username | email           | role
-------------------------------------|----------|-----------------|------
a1b2c3d4-e5f6-7890-abcd-ef1234567890 | admin    | <EMAIL>  | admin
b2c3d4e5-f6g7-8901-bcde-f23456789012 | user     | <EMAIL>   | user
```

## 🚀 اختبار تسجيل الدخول

الآن يمكنك تسجيل الدخول:

- **المسؤول**: admin / 123456789
- **المستخدم**: user / 123456789

## 📋 ملاحظات مهمة

1. **ترتيب العمليات مهم:**
   - database_setup.sql أولاً
   - إنشاء المستخدمين في Auth ثانياً
   - إضافة بيانات user_profiles أخيراً

2. **UUIDs يجب أن تكون حقيقية:**
   - لا تستخدم UUIDs وهمية
   - انسخ من Supabase Auth فقط

3. **التحقق من النجاح:**
   - تأكد من وجود المستخدمين في كلا الجدولين
   - اختبر تسجيل الدخول

## 🔧 إذا استمر الخطأ

إذا استمر الخطأ:

1. **احذف البيانات الخاطئة:**
   ```sql
   DELETE FROM user_profiles WHERE id = '00000000-0000-0000-0000-000000000001';
   DELETE FROM user_profiles WHERE id = '00000000-0000-0000-0000-000000000002';
   ```

2. **تأكد من وجود المستخدمين في Auth:**
   ```sql
   -- هذا لن يعمل لأن auth.users محمي، لكن تحقق من واجهة Authentication
   ```

3. **أعد المحاولة بـ UUIDs صحيحة**

**المشكلة محلولة! ✅**
