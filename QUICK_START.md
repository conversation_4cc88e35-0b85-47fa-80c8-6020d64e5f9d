# 🚀 دليل البدء السريع

## الطريقة السريعة (5 دقائق)

### 1. إنشاء مشروع Supabase
```bash
# اذهب إلى supabase.com
# أنشئ مشروع جديد
# احفظ Project URL و API Key
```

### 2. تشغيل SQL Script
```sql
-- انسخ محتوى ملف database_setup.sql
-- الصقه في SQL Editor في Supabase
-- اضغط Run
```

### 3. تحديث إعدادات التطبيق
```dart
// في lib/config/supabase_config.dart
static const String supabaseUrl = 'YOUR_URL_HERE';
static const String supabaseAnonKey = 'YOUR_KEY_HERE';
```

### 4. إنشاء مستخدم مسؤول
```bash
# في Supabase Dashboard:
# Authentication > Users > Add User
# Table Editor > user_profiles > Insert Row
# id: [User ID], email: <EMAIL>, role: admin
```

### 5. تشغيل التطبيق
```bash
flutter pub get
flutter run
```

---

## الطريقة التفصيلية

اتبع ملف `SUPABASE_SETUP.md` للحصول على تعليمات مفصلة خطوة بخطوة.

---

## حل المشاكل الشائعة

### ❌ خطأ في الاتصال
```
✅ تأكد من صحة URL و API Key
✅ تأكد من اتصال الإنترنت
```

### ❌ خطأ في تسجيل الدخول
```
✅ تأكد من إنشاء المستخدم في Authentication
✅ تأكد من إضافة بياناته في user_profiles
```

### ❌ خطأ في الصلاحيات
```
✅ تأكد من تفعيل RLS
✅ تأكد من إنشاء Policies
```

---

## بيانات تجريبية

### مستخدم مسؤول:
- Email: <EMAIL>
- Password: 123456789
- Role: admin

### مستخدم عادي:
- Email: <EMAIL>  
- Password: 123456789
- Role: user

---

## أوامر مفيدة

```bash
# تحليل الكود
flutter analyze

# تشغيل الاختبارات
flutter test

# بناء التطبيق
flutter build apk

# تنظيف المشروع
flutter clean
flutter pub get
```

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع ملف `SUPABASE_SETUP.md`
2. تحقق من ملف `database_setup.sql`
3. راجع إعدادات Supabase Dashboard

---

## 🎯 الخطوات التالية

بعد إعداد التطبيق:
1. جرب تسجيل الدخول
2. أضف بعض الناخبين
3. جرب البحث والتصفية
4. اختبر الصلاحيات المختلفة

**استمتع بتطبيقك! 🎉**
